from django.urls import path, re_path
from .views import *

app_name = 'appcenter'

urlpatterns = [
    path('', index, name='index'),
    path('predict/', predict, name='predict'),
    path('sentiment/', sentiment_analysis, name='sentiment_analysis'),
    re_path(r'^table1/(?:(?P<page_number>\d+)/)?', table1, name='table1'),
    re_path(r'^table2/(?:(?P<page_number>\d+)/)?', table2, name='table2'),
    path('bi/', bi, name='bi'),
    path('refresh_data', refresh_data, name='refresh_data'),
    path('test',test,name='test')

]
