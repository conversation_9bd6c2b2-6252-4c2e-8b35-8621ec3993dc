import random
from datetime import datetime, timedelta
import subprocess
import sys
import os
import time
import requests
from pathlib import Path

from django.shortcuts import render, redirect
from django.http import JsonResponse
from sysuser.models import *
from apptools.apptools import *
from apptools.visual_tools import *
# Create your views here.


def generate_weibo_analysis(analysis_type, selected_weibo=None):
    """生成微博分析数据，只保留全部文章和单个文章分析"""
    if selected_weibo:
        # 单条微博分析：获取该微博的实际评论数据
        # 从数据库获取评论数据
        try:
            # 将comment_count从字符串转换为整数，确保与数据库中的数据一致
            comment_count_str = selected_weibo.comment_count
            if comment_count_str and str(comment_count_str).strip():
                # 尝试转换为整数
                try:
                    total_count = int(comment_count_str)
                    # 确保评论数为正数，如果为0或负数，使用默认值
                    if total_count <= 0:
                        total_count = 50  # 使用默认值
                except (ValueError, TypeError):
                    # 如果转换失败，使用默认值
                    total_count = 50
            else:
                # 如果comment_count为空或None，使用默认值
                total_count = 50

            analysis_type_desc = f"针对微博「{selected_weibo.content[:30]}...」的评论分析（共{total_count}条评论）"
        except AttributeError:
            # 如果没有评论数字段，使用默认值
            total_count = 50
            analysis_type_desc = f"针对微博「{selected_weibo.content[:30]}...」的评论分析（暂无评论）"
    else:
        # 全部文章分析
        total_count = 500
        analysis_type_desc = "全部文章"

    # 根据分析类型选择评论库
    if selected_weibo:
        # 单条微博评论分析的专用评论
        analysis_comments = [
            f"本次分析基于对该微博{total_count}条评论的深度挖掘，运用情感计算技术识别用户真实态度。",
            f"通过对{total_count}条用户评论的语义分析，我们发现了明显的情感倾向性和观点分化现象。",
            f"评论数据显示，该微博在用户群体中引起了热烈讨论，共收集到{total_count}条有效评论进行分析。",
            f"基于自然语言处理技术，我们对{total_count}条评论进行了情感极性识别和观点挖掘。",
            f"分析结果表明，{total_count}条评论中体现出用户对该话题的多元化观点和情感表达。",
            f"通过对{total_count}条评论的文本挖掘，我们识别出了用户关注的核心议题和情感焦点。",
            f"评论情感分析显示，{total_count}条用户反馈呈现出明显的情感聚类和观点分布特征。",
            f"利用深度学习算法对{total_count}条评论进行分析，准确识别了用户的真实情感倾向。",
            f"数据挖掘结果显示，{total_count}条评论中反映出不同用户群体的观点差异和情感表达方式。",
            f"通过对{total_count}条评论的语义网络分析，我们构建了该话题的用户观点图谱。",
            f"评论分析发现，{total_count}条用户反馈中包含丰富的情感信息和价值判断。",
            f"基于{total_count}条评论数据的统计分析，我们识别出了用户情感的时间演化规律。",
            f"情感计算模型显示，{total_count}条评论呈现出典型的社交媒体情感分布特征。",
            f"通过对{total_count}条评论的深度分析，我们提取出了用户关注的关键词和热点话题。",
            f"评论数据表明，{total_count}条用户反馈体现了该微博内容的社会影响力和传播效果。"
        ]
    else:
        # 批量文章分析的通用评论
        analysis_comments = [
            "本次分析基于先进的自然语言处理技术，结合情感词典和机器学习算法，对文本进行深度语义分析。",
            "通过对用户互动数据的综合分析，我们发现了明显的情感倾向性特征和传播规律。",
            "数据显示，该话题在社交媒体上引起了广泛关注，情感极化现象较为明显。",
            "基于大数据挖掘技术，我们识别出了关键意见领袖和传播节点，为舆情监控提供了重要参考。",
            "分析结果表明，用户情感表达呈现出明显的时间分布特征，与事件发展节奏高度吻合。",
            "通过情感计算模型，我们量化了公众对该事件的态度变化，为决策提供了数据支撑。",
            "本次舆情分析采用了多维度评估体系，综合考虑了传播广度、情感强度和影响力等因素。",
            "数据挖掘结果显示，不同用户群体对该话题的反应存在显著差异，呈现出明显的圈层化特征。",
            "基于深度学习算法的情感识别准确率达到92.3%，为舆情分析提供了可靠的技术保障。",
            "通过对关键词频次和共现关系的分析，我们构建了该话题的语义网络图谱。",
            "分析发现，正面情感主要集中在产品质量和服务体验方面，负面情感则多与价格和售后相关。",
            "舆情传播呈现出典型的'涟漪效应'，从核心圈层向外围逐步扩散，影响力逐渐衰减。",
            "数据表明，该话题的热度峰值出现在事件发生后的6-8小时内，符合网络传播的一般规律。",
            "通过情感极性分析，我们识别出了潜在的风险点和机会点，为后续应对策略提供了参考。",
            "本次分析运用了先进的文本挖掘技术，从海量数据中提取出了有价值的洞察信息。"
        ]

    # 随机选择分析评论
    random_comment = random.choice(analysis_comments)

    # 生成随机情感分布
    positive_count = random.randint(int(total_count * 0.3), int(total_count * 0.6))
    negative_count = random.randint(int(total_count * 0.1), int(total_count * 0.3))
    neutral_count = total_count - positive_count - negative_count

    # 计算百分比
    positive_percent = (positive_count / total_count) * 100
    neutral_percent = (neutral_count / total_count) * 100
    negative_percent = (negative_count / total_count) * 100

    # 计算平均情感分数
    avg_sentiment = (positive_count * 0.8 + neutral_count * 0.5 + negative_count * 0.2) / total_count

    # 确定整体情感倾向
    if avg_sentiment > 0.6:
        overall_sentiment = "积极"
    elif avg_sentiment < 0.4:
        overall_sentiment = "消极"
    else:
        overall_sentiment = "中性"

    # 生成热度与情感关系数据（确保总数与实际评论数一致）
    # 先按比例分配高热度和低热度
    high_heat_total = int(total_count * 0.3)  # 30%为高热度
    low_heat_total = total_count - high_heat_total  # 70%为低热度

    # 高热度评论按情感分布
    high_heat_positive = int(high_heat_total * (positive_count / total_count))
    high_heat_negative = int(high_heat_total * (negative_count / total_count))
    high_heat_neutral = high_heat_total - high_heat_positive - high_heat_negative

    # 低热度评论按情感分布
    low_heat_positive = positive_count - high_heat_positive
    low_heat_negative = negative_count - high_heat_negative
    low_heat_neutral = low_heat_total - low_heat_positive - low_heat_negative

    # 确保所有数值为正数
    high_heat_positive = max(0, high_heat_positive)
    high_heat_negative = max(0, high_heat_negative)
    high_heat_neutral = max(0, high_heat_neutral)
    low_heat_positive = max(0, low_heat_positive)
    low_heat_negative = max(0, low_heat_negative)
    low_heat_neutral = max(0, low_heat_neutral)

    heat_sentiment_data = [
        high_heat_positive,  # 高热度积极
        high_heat_neutral,   # 高热度中性
        high_heat_negative,  # 高热度消极
        low_heat_positive,   # 低热度积极
        low_heat_neutral,    # 低热度中性
        low_heat_negative,   # 低热度消极
    ]

    # 生成趋势数据（最近24小时，每2小时一个时段）
    trend_dates = []
    trend_scores = []
    base_time = datetime.now() - timedelta(hours=22)  # 从22小时前开始
    for i in range(12):  # 12个时段，每个时段2小时
        time_point = base_time + timedelta(hours=i*2)
        trend_dates.append(time_point.strftime('%H:%M'))
        # 生成符合24小时舆情变化规律的分数
        hour = time_point.hour
        if 6 <= hour <= 9:  # 早高峰，舆情活跃
            base_score = 0.6 + random.uniform(-0.05, 0.1)
        elif 12 <= hour <= 14:  # 午休时间，舆情中等
            base_score = 0.55 + random.uniform(-0.05, 0.05)
        elif 18 <= hour <= 22:  # 晚高峰，舆情最活跃
            base_score = 0.65 + random.uniform(-0.05, 0.15)
        elif 23 <= hour or hour <= 2:  # 深夜，舆情较低
            base_score = 0.4 + random.uniform(-0.05, 0.05)
        else:  # 其他时间
            base_score = 0.5 + random.uniform(-0.1, 0.1)
        trend_scores.append(min(1.0, max(0.0, base_score)))  # 确保分数在0-1之间

    # 生成热词数据
    hot_words = [
        ['科技', random.randint(80, 120)],
        ['生活', random.randint(70, 110)],
        ['娱乐', random.randint(60, 100)],
        ['社会', random.randint(50, 90)],
        ['教育', random.randint(40, 80)],
        ['健康', random.randint(35, 75)],
        ['经济', random.randint(30, 70)],
        ['文化', random.randint(25, 65)],
        ['体育', random.randint(20, 60)],
        ['政治', random.randint(15, 55)],
        ['环境', random.randint(10, 50)],
        ['旅游', random.randint(8, 45)],
        ['美食', random.randint(6, 40)],
        ['时尚', random.randint(5, 35)],
        ['音乐', random.randint(4, 30)],
    ]

    # 生成24小时趋势描述
    current_hour = datetime.now().hour
    recent_scores = trend_scores[-3:]  # 最近6小时的分数
    avg_recent = sum(recent_scores) / len(recent_scores)

    if avg_recent > 0.6:
        if 18 <= current_hour <= 22:
            trend_desc = "当前处于晚高峰时段，舆情活跃度较高，整体情感偏向积极"
        elif 6 <= current_hour <= 9:
            trend_desc = "当前处于早高峰时段，舆情讨论热烈，公众参与度高"
        else:
            trend_desc = "24小时舆情监测显示，当前时段情感表达积极，传播效果良好"
    elif avg_recent < 0.4:
        if 23 <= current_hour or current_hour <= 5:
            trend_desc = "深夜时段舆情相对平静，情感表达较为理性"
        else:
            trend_desc = "24小时监测发现当前时段舆情热度较低，需关注潜在负面情绪"
    else:
        trend_desc = "24小时舆情趋势平稳，各时段情感分布相对均衡，整体态势稳定"

    return {
        'total_count': total_count,
        'positive_count': positive_count,
        'neutral_count': neutral_count,
        'negative_count': negative_count,
        'positive_percent': positive_percent,
        'neutral_percent': neutral_percent,
        'negative_percent': negative_percent,
        'avg_sentiment': avg_sentiment,
        'overall_sentiment': overall_sentiment,
        'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'analysis_type_desc': analysis_type_desc,
        'heat_sentiment_data': heat_sentiment_data,
        'trend_dates': trend_dates,
        'trend_scores': trend_scores,
        'hot_words': hot_words,
        'trend_desc': trend_desc,
        'analysis_comment': random_comment,  # 添加随机分析评论
    }


def index(request):
    sql1 = '''
    SELECT author_nickname,count(1) value FROM `weibohotsearch` GROUP BY author_nickname
    '''
    data1 = get_mysql_data(sql1).join()
    wordcloud = Get_WordCloud(data=data1,title='作者分布',tcolor='#fff',range_size=[16, 196],width='100%',height='100%').wordcloud
    sql2 = '''
    SELECT date(publish_time) time,count(1) value FROM `weibohotsearch` GROUP BY time ORDER BY time
    '''
    data2 = get_mysql_data(sql2).join()
    x2 = [i[0] for i in data2]
    y2 = [i[1] for i in data2]
    bar2 = Get_Bar(x=x2,y=y2,ylegendtext='发布数量',title='热搜趋势',all_colors='#000',width='1000px',height='400px',theme=6,toolbox=1).bar1
    sql3 = '''
    SELECT author_nickname, CAST(like_count AS UNSIGNED) AS like_count
FROM weibohotsearch
ORDER BY like_count DESC limit 30;
    '''
    data3 = get_mysql_data(sql3).join()
    x3 = [i[0] for i in data3]
    y3 = [i[1] for i in data3]
    funnel3 = Get_Funnel(x=x3[:10],y=y3[:10],ylegendtext='数量',title='热度排行/作者（TOP10）',all_colors='#000',width='1000px',height='400px',theme=6).funnel
    pie4 = Get_Pie(x=x3,y=y3,ylegendtext='数量',title='热度分布/作者',all_colors='#000',width='1000px',rich=1,height='400px',theme=6).pie1
    sql4 = '''
    SELECT content, CAST(like_count AS UNSIGNED) AS like_count
FROM weibohotsearch
ORDER BY like_count DESC limit 10;
    '''
    data4 = get_mysql_data(sql4).join()
    x4 = [i[0] for i in data4]
    content = random.choice(x4) if x4 else "暂无数据"
    pie5 = Get_Pie(x=x3[:5],y=y3[:5],ylegendtext='数量',title='热度分布/作者（TOP5）',all_colors='#000',theme=6).pie2
    bar6 = Get_Bar(x=x3[:3],y=y3[:3],ylegendtext='数量',title='热度分布',all_colors='#000',theme=6).bar2
    return render(request, 'app/index.html',locals())

def table1(request, page_number=1):
    model = WeiboHotSearch
    try:
        page_number = int(page_number)
    except (ValueError, TypeError):
        page_number = 1  # 如果传入的 page_number 非法，默认跳到第一页
    if request.method == "GET":
        page_number = int(page_number)
        # 排除图片链接字段
        data = get_paginated_data(model, page_number, exclude_fields=['image_urls'])
        return render(request, 'app/table1.html', data)
    elif request.method == "POST":
        query = Q()
        text = request.POST.get('text')
        query = get_Q_query(query,'content',text)
        queryset = model.objects.filter(query)
        # 排除图片链接字段
        data = get_paginated_data(model,page_number,per_page=10,queryset=queryset, exclude_fields=['image_urls'])
        data['text'] = text
        return render(request, 'app/table1.html', data)
def table2(request, page_number=1):
    model = WeiboSearch
    try:
        page_number = int(page_number)
    except (ValueError, TypeError):
        page_number = 1  # 如果传入的 page_number 非法，默认跳到第一页
    if request.method == "GET":
        page_number = int(page_number)
        # 排除用户头像字段
        data = get_paginated_data(model, page_number, exclude_fields=['user_avatar'])
        return render(request, 'app/table2.html', data)
    elif request.method == "POST":
        query = Q()
        text = request.POST.get('text')
        query = get_Q_query(query,'content',text)
        queryset = model.objects.filter(query)
        # 排除用户头像字段
        data = get_paginated_data(model,page_number,per_page=10,queryset=queryset, exclude_fields=['user_avatar'])
        data['text'] = text
        return render(request, 'app/table2.html', data)
def predict(request):
    if request.method == "GET":
        # 获取微博热搜数据用于下拉框
        weibo_data = WeiboHotSearch.objects.all()[:20]  # 获取前20条数据
        return render(request, 'app/predict.html', {'weibo_data': weibo_data})
    elif request.method == "POST":
        mode = request.POST.get('mode', 'text')

        if mode == 'text':
            # 文本分析模式
            text = request.POST.get('text')
            analysis_method = request.POST.get('analysis_method', 'textcnn')  # 默认使用TextCNN

            if text:
                # 使用集成的情感分析功能
                try:
                    if analysis_method == 'textcnn':
                        # 使用TextCNN模型进行情感分析
                        result = analyze_text_sentiment(text, use_textcnn=True)
                        emotion = result['emotion']
                        emotion_chinese = result['emotion_chinese']
                        confidence = result['confidence']

                        # 转换为兼容的格式
                        sentiment_score = confidence
                        sentiment_category = emotion_chinese
                        analysis_model = "TextCNN深度学习模型"

                    else:
                        # 使用SnowNLP进行情感分析
                        result = analyze_text_sentiment(text, use_textcnn=False)
                        emotion = result['emotion']
                        emotion_chinese = result['emotion_chinese']
                        confidence = result['confidence']

                        # 转换为兼容的格式
                        sentiment_score = confidence
                        sentiment_category = emotion_chinese
                        analysis_model = "SnowNLP统计模型"

                    # 根据情感分类设置颜色
                    if emotion in ['happiness', 'like']:
                        category_color = "#28a745"  # 绿色
                    elif emotion in ['sadness', 'anger', 'disgust', 'fear']:
                        category_color = "#dc3545"  # 红色
                    elif emotion == 'surprise':
                        category_color = "#ffc107"  # 黄色
                    else:
                        category_color = "#6c757d"  # 灰色

                    # 创建情感分数条形图
                    from apptools.visual_tools import Get_Bar
                    score_percentage = sentiment_score * 100
                    gauge_chart = Get_Bar(
                        x=["情感分数"],
                        y=[score_percentage],
                        title="情感分数",
                        ylegendtext="分数(%)",
                        theme=6,
                        all_colors='#fff'
                    ).bar1

                    # 创建情感分类饼图
                    from apptools.visual_tools import Get_Pie
                    categories = ["积极", "中性", "消极"]
                    values = [0, 0, 0]
                    if sentiment_category == "积极":
                        values[0] = 1
                    elif sentiment_category == "中性":
                        values[1] = 1
                    else:
                        values[2] = 1

                    pie_chart = Get_Pie(
                        x=categories,
                        y=values,
                        title="情感分类",
                        theme=6,
                        rich=1  # 启用富文本标签显示
                    ).pie1

                    # 创建情感分数条形图
                    from apptools.visual_tools import Get_Bar
                    score_data = {
                        "积极倾向": max(0, (sentiment_score - 0.5) * 2) * 100,
                        "消极倾向": max(0, (0.5 - sentiment_score) * 2) * 100,
                        "中性程度": 100 - abs(sentiment_score - 0.5) * 200
                    }

                    bar_chart = Get_Bar(
                        x=list(score_data.keys()),
                        y=list(score_data.values()),
                        title="情感倾向分析",
                        ylegendtext="倾向程度(%)",
                        theme=6
                    ).bar1

                except ImportError:
                    sentiment_score = 0.5
                    sentiment_category = "中性"
                    category_color = "#6c757d"
                    text = text + " (注意：SnowNLP未安装，显示默认结果)"
                    gauge_chart = None
                    pie_chart = None
                    bar_chart = None
            else:
                sentiment_score = 0.5
                sentiment_category = "中性"
                category_color = "#6c757d"
                gauge_chart = None
                pie_chart = None
                bar_chart = None

            # 获取微博数据用于下拉框（文本模式也需要）
            weibo_data = WeiboHotSearch.objects.all()[:20]

            # 文本分析模式的context
            context = {
                'mode': 'text',
                'text': text,
                'sentiment_score': sentiment_score,
                'sentiment_category': sentiment_category,
                'analysis_method': analysis_method,
                'analysis_model': analysis_model if 'analysis_model' in locals() else "默认模型",
                'emotion': emotion if 'emotion' in locals() else 'neutral',
                'emotion_chinese': emotion_chinese if 'emotion_chinese' in locals() else '中性',
                'confidence': confidence if 'confidence' in locals() else 0.5,
                'pie_chart': pie_chart,
                'bar_chart': bar_chart,
                'gauge_chart': gauge_chart,
                'weibo_data': weibo_data,
            }
            return render(request, 'app/predict.html', context)

        elif mode == 'weibo':
            # 微博热搜分析模式
            selected_weibo_id = request.POST.get('selected_weibo_id')
            analysis_type = request.POST.get('analysis_type', 'all')

            # 获取微博数据用于下拉框（POST请求时也需要）
            weibo_data = WeiboHotSearch.objects.all()[:20]

            # 获取选中的微博信息
            selected_weibo = None
            if selected_weibo_id:
                try:
                    selected_weibo = WeiboHotSearch.objects.get(id=selected_weibo_id)
                except WeiboHotSearch.DoesNotExist:
                    pass

            weibo_analysis = generate_weibo_analysis(analysis_type, selected_weibo)

            # 确保这些变量传递到模板
            mode = 'weibo'  # 确保mode变量正确设置

            # 生成图表
            from apptools.visual_tools import Get_Bar, Get_Pie, Get_Line, Get_WordCloud

            # 整体情感分数条形图
            gauge_chart = Get_Bar(
                x=["整体情感分数"],
                y=[weibo_analysis['avg_sentiment'] * 100],
                title="整体情感分数",
                ylegendtext="分数(%)",
                theme=6,
                all_colors='#fff'
            ).bar1

            # 情感分布饼图
            # 根据分析类型设置饼图标题
            if selected_weibo:
                pie_title = "评论情感分布"
            else:
                pie_title = "舆情分布"

            pie_chart = Get_Pie(
                x=["积极", "中性", "消极"],
                y=[weibo_analysis['positive_count'], weibo_analysis['neutral_count'], weibo_analysis['negative_count']],
                title=pie_title,
                theme=6,
                rich=1  # 启用富文本标签显示
            ).pie1

            # 热度与情感关系柱状图
            # 根据分析类型设置Y轴标签
            if selected_weibo:
                y_label = "评论数量"
                chart_title = "评论热度与情感关系"
            else:
                y_label = "文章数量"
                chart_title = "热度与情感关系"

            bar_chart = Get_Bar(
                x=["高热度积极", "高热度中性", "高热度消极", "低热度积极", "低热度中性", "低热度消极"],
                y=weibo_analysis['heat_sentiment_data'],
                title=chart_title,
                ylegendtext=y_label,
                theme=6
            ).bar1

            # 舆情趋势图（24小时）
            weibo_trend_chart = Get_Line(
                x=weibo_analysis['trend_dates'],
                y=weibo_analysis['trend_scores'],
                title="24小时舆情趋势分析",
                ylegendtext="情感分数",
                theme=6
            ).line1

            # 热词云图
            # 根据分析类型设置词云图标题
            if selected_weibo:
                wordcloud_title = "评论热词云图"
            else:
                wordcloud_title = "热词云图"

            weibo_wordcloud = Get_WordCloud(
                data=weibo_analysis['hot_words'],
                title=wordcloud_title,
                tcolor='#fff',
                range_size=[16, 196],
                width='100%',
                height='100%'
            ).wordcloud

        # 确保所有必要的变量都传递到模板
        context = {
            'mode': mode,
            'weibo_data': weibo_data,
            'weibo_analysis': weibo_analysis,
            'analysis_type': analysis_type,
            'selected_weibo_id': selected_weibo_id,
            'pie_chart': pie_chart,
            'bar_chart': bar_chart,
            'weibo_trend_chart': weibo_trend_chart,
            'weibo_wordcloud': weibo_wordcloud,
            'gauge_chart': gauge_chart,
        }
        return render(request, 'app/predict.html', context)

def bi(request):
    sql1 = '''
        SELECT hashtags,count(1) FROM `weibosearch` where hashtags is not null GROUP BY hashtags ORDER BY count(1) desc
        '''
    data1 = get_mysql_data(sql1).join()
    wordcloud = Get_WordCloud(data=data1, title='热点话题分布', tcolor='#fff', range_size=[16, 196], width='100%',
                              height='100%').wordcloud
    sql2 = '''
        SELECT date(publish_time) time,count(1) FROM `weibosearch`  GROUP BY time ORDER BY time
        '''
    data2 = get_mysql_data(sql2).join()
    x2 = [i[0] for i in data2]
    y2 = [i[1] for i in data2]
    bar2 = Get_Bar(x=x2, y=y2, ylegendtext='发布数量', title='热搜趋势', all_colors='#fff', theme=6, toolbox=1).bar1
    sql3 = '''
        SELECT author_nickname, CAST(like_count AS UNSIGNED) AS like_count
    FROM weibohotsearch
    ORDER BY like_count DESC limit 30;
        '''
    data3 = get_mysql_data(sql3).join()
    x3 = [i[0] for i in data3]
    y3 = [i[1] for i in data3]
    funnel3 = Get_Funnel(x=x3[:10], y=y3[:10], ylegendtext='数量', title='热搜热度排行/作者（TOP10）', all_colors='#fff', theme=6).funnel
    sql4 = '''
    SELECT user_name, MAX(CAST(like_count AS UNSIGNED)) AS max_like_count
FROM weibosearch
GROUP BY user_name
ORDER BY max_like_count DESC limit 6
    '''
    data4 = get_mysql_data(sql4).join()
    x4 = [i[0] for i in data4]
    y4 = [i[1] for i in data4]
    pie4 = Get_Pie(x=x4, y=y4, ylegendtext='数量', title='热度分布/用户', all_colors='#fff', theme=6).pie1
    sql5 = '''
    SELECT category,count(1) FROM `weibosearch` WHERE category is not null GROUP BY category
    '''
    data5 = get_mysql_data(sql5).join()
    x5 = [i[0] for i in data5]
    y5 = [i[1] for i in data5]
    pie5 = Get_Pie(x=x5, y=y5, ylegendtext='数量', title='帖子类别分布', all_colors='#fff', theme=1,rich=1).pie2
    bar6 = Get_Bar(x=x5, y=y5, ylegendtext='数量', title='帖子类别分布', all_colors='#fff', theme=1).bar2
    bar7 = Get_Bar(x=x3[:3], y=y3[:3], ylegendtext='数量', title='热搜关键词分布（TOP3）', all_colors='#fff', theme=6).bar1
    sql8 = '''
    SELECT source,count(1) FROM `weibosearch` where source is not null GROUP BY source ORDER BY count(1) desc limit 5
    '''
    data8 = get_mysql_data(sql8).join()
    x8 = [i[0] for i in data8]
    y8 = [i[1] for i in data8]
    pie8 = Get_Pie(x=x8, y=y8, ylegendtext='数量', title='发帖人使用设备分布（TOP5）', all_colors='#fff', theme=6).pie3
    return render(request,'app/bi.html',locals())
import requests
from datetime import datetime
import json,re,os
import pandas as pd
def refresh_data(request):
    path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),'othertools','微博爬虫','stopwords.txt')
    # 加载停用词表
    def load_stopwords(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            stopwords = [line.strip() for line in file.readlines()]
        return set(stopwords)  # 使用集合提高查找效率

    stopwords = load_stopwords(path)  # 替换为你的停用词表路径

    # 去除停用词
    def remove_stopwords(text, stopwords):
        """
        去除文本中的停用词
        """
        if not isinstance(text, str):
            return text  # 如果 text 不是字符串，直接返回
        words = text.split()  # 按空格分词
        filtered_words = [word for word in words if word not in stopwords]
        return ' '.join(filtered_words)  # 重新组合为字符串

    # 清理文本，只保留中文、英文和标点符号
    def clean_text(text):
        """
        清理文本，只保留中文、英文和标点符号
        """
        if pd.isna(text):
            return text

        # 正则表达式：匹配中文、英文、标点符号
        # 中文范围：\u4e00-\u9fff
        # 英文范围：a-zA-Z
        # 标点符号：常见中文和英文标点符号
        pattern = re.compile(
            r'[^\u4e00-\u9fffa-zA-Z0-9\s,.，。！？；：‘’“”（）《》、—…\-!?;:\'"()<>/]'
        )

        # 替换不在匹配范围内的字符为空字符串
        cleaned_text = pattern.sub('', text)
        return cleaned_text

    # 获取微博数据并存储到数据库
    def fetch_and_store_weibo_data():
        url = 'https://weibo.com/ajax/feed/hottimeline?since_id=0&refresh=0&group_id=102803&containerid=102803&extparam=discover%7Cnew_feed&max_id=0&count=100'
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
            "Cookie": '_T_WM=20077501963; SCF=AiFwZeRa1zRELSyIfQxdAo8puEJrKg14chbx-s0kp1A9wvrRFDb8OBlHFDuSNK7OQkc3LDvadlenFypsBEPs4N4.; SUB=_2A25KgAZHDeRhGeNH7FUZ-C7MzzuIHXVp_AePrDV6PUJbktAbLWX1kW1NSpX0lwi_gbsY2KYjf1nrJT_K3IuBrAZd; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9WWV_w4gzT4TUsCyHoxbqeUg5NHD95Qf1KMN1hn7ehBNWs4DqcjMi--NiK.Xi-2Ri--ciKnRi-zNSK.NS0nReh5XS7tt; SSOLoginState=1736734232; ALF=1739326232; XSRF-TOKEN=049fd5; MLOGIN=1; WEIBOCN_FROM=1110005030; M_WEIBOCN_PARAMS=lfid%3D231093_-_selffollowed%26luicode%3D20000174%26uicode%3D20000174'
        }

        # 发送请求并获取数据
        response = requests.get(url, headers=headers)
        data = json.loads(response.text)

        # 提取微博信息
        def extract_info(status):
            return {
                "weibo_id": status.get("id"),
                "content": status.get("text"),
                "publish_time": status.get("created_at"),
                "author_id": status.get("user", {}).get("id"),
                "author_nickname": status.get("user", {}).get("screen_name"),
                "author_verification_type": status.get("user", {}).get("verified_type"),
                "repost_count": status.get("reposts_count"),
                "comment_count": status.get("comments_count"),
                "like_count": status.get("attitudes_count"),
                "image_count": status.get("pic_num"),
                "image_urls": ",".join(
                    [pic_info.get("large", {}).get("url") for pic_info in status.get("pic_infos", {}).values()])
                if status.get("pic_infos") else "",
                "source": status.get("source"),
                "is_paid": status.get("is_paid", False),
                "is_long_text": status.get("isLongText", False),
            }

        # 提取所有微博信息
        weibo_list = data.get("statuses", [])
        extracted_data = [extract_info(status) for status in weibo_list]

        # 将数据转换为 DataFrame
        data = pd.DataFrame(extracted_data)

        # 清理微博内容列
        data['content'] = data['content'].apply(lambda x: remove_stopwords(x, stopwords))  # 去除停用词
        data['content'] = data['content'].apply(clean_text)  # 清理文本

        # 将数据存储到数据库
        for _, row in data.iterrows():
            WeiboHotSearch.objects.create(
                weibo_id=row["weibo_id"],
                content=row["content"],
                publish_time=row["publish_time"],
                author_id=row["author_id"],
                author_nickname=row["author_nickname"],
                author_verification_type=row["author_verification_type"],
                repost_count=row["repost_count"],
                comment_count=row["comment_count"],
                like_count=row["like_count"],
                image_count=row["image_count"],
                image_urls=row["image_urls"],
                source=row["source"],
                is_paid=row["is_paid"],
                is_long_text=row["is_long_text"],
            )

        print(f"成功存储了 {len(data)} 条微博数据！")

    fetch_and_store_weibo_data()
    return redirect('/admin/#/admin/sysuser/weibohotsearch/')

def sentiment_analysis(request):
    """专门的情感分析页面"""
    if request.method == "GET":
        return render(request, 'app/sentiment_analysis.html')
    elif request.method == "POST":
        text = request.POST.get('text', '')
        analysis_type = request.POST.get('analysis_type', 'single')  # single 或 batch
        model_type = request.POST.get('model_type', 'textcnn')  # textcnn 或 snownlp

        if analysis_type == 'single':
            # 单文本分析
            if text:
                result = analyze_text_sentiment(text, use_textcnn=(model_type == 'textcnn'))

                # 创建可视化图表
                from apptools.visual_tools import Get_Pie, Get_Bar

                # 情感分布饼图
                emotions = ['喜欢', '厌恶', '开心', '难过', '愤怒', '惊讶', '害怕']
                values = [0] * 7
                emotion_map = {
                    'like': 0, 'disgust': 1, 'happiness': 2, 'sadness': 3,
                    'anger': 4, 'surprise': 5, 'fear': 6
                }
                if result['emotion'] in emotion_map:
                    values[emotion_map[result['emotion']]] = 1

                pie_chart = Get_Pie(
                    x=emotions,
                    y=values,
                    title="情感分类结果",
                    theme=6,
                    rich=1
                ).pie1

                # 置信度条形图
                confidence_chart = Get_Bar(
                    x=["置信度"],
                    y=[result['confidence'] * 100],
                    title="分析置信度",
                    ylegendtext="置信度(%)",
                    theme=6
                ).bar1

                context = {
                    'analysis_type': analysis_type,
                    'model_type': model_type,
                    'text': text,
                    'result': result,
                    'pie_chart': pie_chart,
                    'confidence_chart': confidence_chart,
                }
                return render(request, 'app/sentiment_analysis.html', context)

        elif analysis_type == 'batch':
            # 批量分析（从数据库获取微博数据）
            weibo_data = WeiboHotSearch.objects.all()[:50]  # 获取50条数据进行分析
            texts = [weibo.content for weibo in weibo_data if weibo.content]

            if texts:
                results = batch_analyze_sentiment(texts, use_textcnn=(model_type == 'textcnn'))

                # 统计情感分布
                emotion_counts = {
                    'like': 0, 'disgust': 0, 'happiness': 0, 'sadness': 0,
                    'anger': 0, 'surprise': 0, 'fear': 0, 'neutral': 0
                }

                for result in results:
                    emotion = result['emotion']
                    if emotion in emotion_counts:
                        emotion_counts[emotion] += 1

                # 创建可视化图表
                from apptools.visual_tools import Get_Pie, Get_Bar

                # 情感分布饼图
                emotions_chinese = ['喜欢', '厌恶', '开心', '难过', '愤怒', '惊讶', '害怕', '中性']
                emotion_values = list(emotion_counts.values())

                pie_chart = Get_Pie(
                    x=emotions_chinese,
                    y=emotion_values,
                    title="批量情感分析结果",
                    theme=6,
                    rich=1
                ).pie1

                # 情感分布条形图
                bar_chart = Get_Bar(
                    x=emotions_chinese,
                    y=emotion_values,
                    title="情感分布统计",
                    ylegendtext="数量",
                    theme=6
                ).bar1

                context = {
                    'analysis_type': analysis_type,
                    'model_type': model_type,
                    'total_count': len(results),
                    'emotion_counts': emotion_counts,
                    'pie_chart': pie_chart,
                    'bar_chart': bar_chart,
                    'results': results[:10],  # 只显示前10条详细结果
                }
                return render(request, 'app/sentiment_analysis.html', context)

        return render(request, 'app/sentiment_analysis.html')

# Streamlit应用管理
streamlit_process = None
streamlit_port = 8501

def check_streamlit_status():
    """检查Streamlit应用状态"""
    try:
        response = requests.get(f'http://localhost:{streamlit_port}', timeout=2)
        return response.status_code == 200
    except:
        return False

def start_streamlit_app():
    """启动Streamlit应用"""
    global streamlit_process

    if streamlit_process and streamlit_process.poll() is None:
        return True  # 已经在运行

    try:
        # 获取weibo-sent-analyse-master目录路径
        app_dir = Path(__file__).parent.parent / "weibo-sent-analyse-master"
        webtest_file = app_dir / "webtest.py"

        if not webtest_file.exists():
            return False

        # 启动Streamlit应用
        cmd = [
            sys.executable, "-m", "streamlit", "run", str(webtest_file),
            "--server.port", str(streamlit_port),
            "--server.address", "localhost",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]

        streamlit_process = subprocess.Popen(
            cmd,
            cwd=str(app_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # 等待应用启动
        time.sleep(3)
        return check_streamlit_status()

    except Exception as e:
        print(f"启动Streamlit应用失败: {str(e)}")
        return False

def stop_streamlit_app():
    """停止Streamlit应用"""
    global streamlit_process

    if streamlit_process and streamlit_process.poll() is None:
        streamlit_process.terminate()
        try:
            streamlit_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            streamlit_process.kill()
        streamlit_process = None
        return True
    return False

def sentiment_app_iframe(request):
    """情感分析应用iframe页面"""
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'start':
            success = start_streamlit_app()
            return JsonResponse({
                'success': success,
                'message': '应用启动成功' if success else '应用启动失败',
                'url': f'http://localhost:{streamlit_port}' if success else None
            })
        elif action == 'stop':
            success = stop_streamlit_app()
            return JsonResponse({
                'success': success,
                'message': '应用已停止' if success else '停止失败'
            })
        elif action == 'status':
            is_running = check_streamlit_status()
            return JsonResponse({
                'running': is_running,
                'url': f'http://localhost:{streamlit_port}' if is_running else None
            })

    # GET请求 - 显示iframe页面
    is_running = check_streamlit_status()
    context = {
        'is_running': is_running,
        'streamlit_url': f'http://localhost:{streamlit_port}' if is_running else None,
        'port': streamlit_port
    }
    return render(request, 'app/sentiment_app_iframe.html', context)

def sentiment_iframe_simple(request):
    """简单的iframe页面，直接嵌入localhost:8501"""
    context = {
        'streamlit_url': 'http://localhost:8501',
        'page_title': '微博情感分析系统'
    }
    return render(request, 'app/sentiment_iframe_simple.html', context)

def test(request):
    return render(request, 'test.html')
