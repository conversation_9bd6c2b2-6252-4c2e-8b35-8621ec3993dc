"""
微博情感分析模块
集成weibo-sent-analyse-master的TextCNN模型
"""
import os
import torch
import pickle as pkl
import numpy as np
from django.conf import settings
import torch.nn.functional as F
from snownlp import SnowNLP


class Config:
    """配置参数"""
    def __init__(self, dataset, embedding):
        self.model_name = 'TextCNN'
        self.train_path = dataset + '/data/train.txt'
        self.dev_path = dataset + '/data/dev.txt'
        self.test_path = dataset + '/data/test.txt'
        self.class_list = [x.strip() for x in open(
            dataset + '/data/class3.txt', encoding='utf-8').readlines()]
        self.vocab_path = dataset + '/data/vocab3.pkl'
        self.save_path = dataset + '/saved_dict/' + self.model_name + '.ckpt'
        self.log_path = dataset + '/log/' + self.model_name
        self.embedding_pretrained = None  # 不使用预训练embedding
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.dropout = 0.4  # 与原模型一致
        self.require_improvement = 1000
        self.num_classes = len(self.class_list)
        self.n_vocab = 0
        self.num_epochs = 20
        self.batch_size = 128
        self.pad_size = 128  # 与原模型一致
        self.learning_rate = 1e-3
        self.embed = 300
        self.filter_sizes = (1, 2, 3, 4)  # 与原模型一致
        self.num_filters = 256


class TextCNN(torch.nn.Module):
    """TextCNN模型"""
    def __init__(self, config):
        super(TextCNN, self).__init__()
        if config.embedding_pretrained is not None:
            self.embedding = torch.nn.Embedding.from_pretrained(config.embedding_pretrained, freeze=False)
        else:
            self.embedding = torch.nn.Embedding(config.n_vocab, config.embed, padding_idx=config.n_vocab - 1)
        self.convs = torch.nn.ModuleList(
            [torch.nn.Conv2d(1, config.num_filters, (k, config.embed)) for k in config.filter_sizes])
        self.dropout = torch.nn.Dropout(config.dropout)
        self.fc = torch.nn.Linear(config.num_filters * len(config.filter_sizes), config.num_classes)

    def conv_and_pool(self, x, conv):
        x = F.relu(conv(x)).squeeze(3)
        x = F.avg_pool1d(x, x.size(2)).squeeze(2)  # 使用avg_pool1d与原模型一致
        return x

    def forward(self, x):
        out = self.embedding(x[0])
        out = out.unsqueeze(1)
        out = torch.cat([self.conv_and_pool(out, conv) for conv in self.convs], 1)
        out = self.dropout(out)
        out = self.fc(out)
        return out


class SentimentAnalyzer:
    """情感分析器"""

    def __init__(self):
        self.model = None
        self.config = None
        self.vocab = None
        self.labels = {
            0: 'like',      # 喜欢
            1: 'disgust',   # 厌恶
            2: 'happiness', # 开心
            3: 'sadness',   # 难过
            4: 'anger',     # 愤怒
            5: 'surprise',  # 惊讶
            6: 'fear'       # 害怕
        }
        self.load_model()

    def load_model(self):
        """加载预训练模型"""
        try:
            # 模型文件路径
            model_dir = os.path.join(settings.BASE_DIR, 'weibo-sent-analyse-master', 'mydata')
            vocab_path = os.path.join(model_dir, 'data', 'vocab3.pkl')
            model_path = os.path.join(model_dir, 'saved_dict', 'cnn6.ckpt')

            # 检查文件是否存在
            if not os.path.exists(vocab_path):
                print(f"词汇表文件不存在: {vocab_path}")
                return False
            if not os.path.exists(model_path):
                print(f"模型文件不存在: {model_path}")
                return False

            # 加载词汇表
            self.vocab = pkl.load(open(vocab_path, 'rb'))

            # 初始化配置（修复embedding参数）
            self.config = Config(model_dir, 'random')  # 使用random而不是'123'
            self.config.n_vocab = len(self.vocab)

            # 初始化模型
            self.model = TextCNN(self.config)

            # 加载模型权重
            self.model.load_state_dict(torch.load(model_path, map_location='cpu'))
            self.model.eval()

            print("TextCNN模型加载成功")
            return True

        except Exception as e:
            print(f"模型加载失败: {str(e)}")
            return False

    def preprocess_text(self, text):
        """文本预处理"""
        if not text:
            return None

        tokenizer = lambda x: [y for y in x]
        pad_size = 32  # 保持32，因为模型是用32训练的

        # 文本预处理 - 去除特殊字符，只保留中文
        import re
        pattern = re.compile(r'[^\u4e00-\u9fa5]')
        text = re.sub(pattern, '', text)

        # 分词
        words = tokenizer(text)

        # 转换为ID
        words_id = []
        for word in words:
            words_id.append(self.vocab.get(word, self.vocab.get('<UNK>', 0)))

        # 填充或截断
        if len(words_id) < pad_size:
            words_id.extend([self.vocab.get('<PAD>', 0)] * (pad_size - len(words_id)))
        else:
            words_id = words_id[:pad_size]

        return torch.LongTensor([words_id])

    def predict_textcnn(self, text):
        """使用TextCNN模型进行情感预测"""
        if not self.model or not self.vocab:
            return self.predict_snownlp(text)  # 降级到SnowNLP

        try:
            # 预处理文本
            input_data = self.preprocess_text(text)
            if input_data is None:
                return "neutral", 0.5

            # 模型预测
            with torch.no_grad():
                outputs = self.model((input_data,))
                predicted = torch.max(outputs.data, 1)[1].cpu().numpy()
                probabilities = F.softmax(outputs, dim=1).cpu().numpy()

                # 获取预测结果
                emotion_label = self.labels.get(predicted[0], 'neutral')
                confidence = float(probabilities[0][predicted[0]])

                return emotion_label, confidence

        except Exception as e:
            print(f"TextCNN预测失败: {str(e)}")
            return self.predict_snownlp(text)  # 降级到SnowNLP

    def predict_snownlp(self, text):
        """使用SnowNLP进行情感预测（备用方案）"""
        try:
            s = SnowNLP(text)
            sentiment_score = s.sentiments

            if sentiment_score > 0.6:
                return "happiness", sentiment_score
            elif sentiment_score < 0.4:
                return "sadness", sentiment_score
            else:
                return "neutral", sentiment_score

        except Exception as e:
            print(f"SnowNLP预测失败: {str(e)}")
            return "neutral", 0.5

    def analyze_sentiment(self, text, use_textcnn=True):
        """情感分析主函数"""
        if use_textcnn and self.model:
            emotion, confidence = self.predict_textcnn(text)
        else:
            emotion, confidence = self.predict_snownlp(text)

        # 转换为中文标签
        emotion_map = {
            'like': '喜欢',
            'disgust': '厌恶',
            'happiness': '开心',
            'sadness': '难过',
            'anger': '愤怒',
            'surprise': '惊讶',
            'fear': '害怕',
            'neutral': '中性'
        }

        emotion_chinese = emotion_map.get(emotion, '中性')

        return {
            'emotion': emotion,
            'emotion_chinese': emotion_chinese,
            'confidence': confidence,
            'text': text
        }


# 全局情感分析器实例
sentiment_analyzer = SentimentAnalyzer()


def analyze_text_sentiment(text, use_textcnn=True):
    """
    分析文本情感的便捷函数

    Args:
        text (str): 要分析的文本
        use_textcnn (bool): 是否使用TextCNN模型

    Returns:
        dict: 包含情感分析结果的字典
    """
    return sentiment_analyzer.analyze_sentiment(text, use_textcnn)


def batch_analyze_sentiment(texts, use_textcnn=True):
    """
    批量分析文本情感

    Args:
        texts (list): 文本列表
        use_textcnn (bool): 是否使用TextCNN模型

    Returns:
        list: 情感分析结果列表
    """
    results = []
    for text in texts:
        result = sentiment_analyzer.analyze_sentiment(text, use_textcnn)
        results.append(result)
    return results
