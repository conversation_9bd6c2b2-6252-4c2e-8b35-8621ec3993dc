<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>page</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="f35462a1ed4a4fe2b32b2a50a9944b5d" class="chart-container" style="width:700px; height:500px; "></div>
    <script>
        var chart_f35462a1ed4a4fe2b32b2a50a9944b5d = echarts.init(
            document.getElementById('f35462a1ed4a4fe2b32b2a50a9944b5d'), 'white', {renderer: 'canvas'});
        var option_f35462a1ed4a4fe2b32b2a50a9944b5d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "radar",
            "name": "\u4eba\u6570\u96f7\u8fbe\u56fe",
            "data": [
                [
                    1264,
                    2167,
                    908,
                    604,
                    234,
                    765,
                    60
                ]
            ],
            "label": {
                "show": true,
                "margin": 8
            },
            "selectedMode": false,
            "zlevel": 0,
            "z": 2,
            "itemStyle": {
                "normal": {
                    "color": [
                        "#4e79a7"
                    ]
                }
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u4eba\u6570\u96f7\u8fbe\u56fe"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "radar": [
        {
            "indicator": [
                {
                    "name": "happiness",
                    "max": 2167
                },
                {
                    "name": "like",
                    "max": 2167
                },
                {
                    "name": "anger",
                    "max": 2167
                },
                {
                    "name": "surprise",
                    "max": 2167
                },
                {
                    "name": "disgust",
                    "max": 2167
                },
                {
                    "name": "sadness",
                    "max": 2167
                },
                {
                    "name": "fear",
                    "max": 2167
                }
            ],
            "startAngle": 90,
            "name": {
                "textStyle": {
                    "color": "#000"
                }
            },
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0
            }
        }
    ],
    "title": [
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_f35462a1ed4a4fe2b32b2a50a9944b5d.setOption(option_f35462a1ed4a4fe2b32b2a50a9944b5d);
    </script>
</body>
</html>
