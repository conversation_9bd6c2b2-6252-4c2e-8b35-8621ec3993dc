<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>page</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="83a73a8c53534ed3a5d4de077a349028" class="chart-container" style="width:700px; height:400px; "></div>
    <script>
        var chart_83a73a8c53534ed3a5d4de077a349028 = echarts.init(
            document.getElementById('83a73a8c53534ed3a5d4de077a349028'), 'white', {renderer: 'canvas'});
        var option_83a73a8c53534ed3a5d4de077a349028 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5404\u7701\u4eba\u6570",
            "legendHoverLink": true,
            "data": [
                30,
                8,
                4,
                30,
                10,
                6,
                10,
                93,
                13,
                14,
                32,
                6,
                33,
                12,
                6,
                6,
                27,
                42,
                8,
                6,
                6,
                6,
                2,
                2,
                12,
                14,
                2,
                7,
                2,
                2,
                6,
                4,
                11
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5404\u7701\u4eba\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -60,
                "margin": 8,
                "fontSize": 7
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u56db\u5ddd",
                "\u91cd\u5e86",
                "\u5b89\u5fbd",
                "\u6d59\u6c5f",
                "\u9ed1\u9f99\u6c5f",
                "\u6cb3\u5317",
                "\u9a6c\u6765\u897f\u4e9a",
                "\u5e7f\u4e1c",
                "\u4e2d\u56fd\u53f0\u6e7e",
                "\u6cb3\u5357",
                "\u5317\u4eac",
                "\u4e2d\u56fd\u9999\u6e2f",
                "\u5c71\u4e1c",
                "\u8fbd\u5b81",
                "\u6e56\u5317",
                "\u6fb3\u5927\u5229\u4e9a",
                "\u4e0a\u6d77",
                "\u6c5f\u82cf",
                "\u5c71\u897f",
                "\u5929\u6d25",
                "\u8d35\u5dde",
                "\u6c5f\u897f",
                "\u82f1\u56fd",
                "\u6e56\u5357",
                "\u798f\u5efa",
                "\u4e2d\u56fd\u6fb3\u95e8",
                "\u9655\u897f",
                "\u5409\u6797",
                "\u5b81\u590f",
                "\u7f8e\u56fd",
                "\u65b0\u7586",
                "\u5185\u8499\u53e4",
                "\u65b0\u52a0\u5761"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_83a73a8c53534ed3a5d4de077a349028.setOption(option_83a73a8c53534ed3a5d4de077a349028);
    </script>
</body>
</html>
