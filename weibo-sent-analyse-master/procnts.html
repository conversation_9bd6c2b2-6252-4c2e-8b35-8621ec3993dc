<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>page</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="616146a9926b4ce3bc2ff1bbb975b5e4" class="chart-container" style="width:700px; height:400px; "></div>
    <script>
        var chart_616146a9926b4ce3bc2ff1bbb975b5e4 = echarts.init(
            document.getElementById('616146a9926b4ce3bc2ff1bbb975b5e4'), 'white', {renderer: 'canvas'});
        var option_616146a9926b4ce3bc2ff1bbb975b5e4 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5404\u7701\u4eba\u6570",
            "legendHoverLink": true,
            "data": [
                469,
                206,
                182,
                288,
                855,
                122,
                104,
                147,
                123,
                290,
                352,
                294,
                80,
                7,
                164,
                176,
                298,
                250,
                318,
                40,
                130,
                225,
                18,
                2,
                136,
                91,
                141,
                42,
                77,
                127,
                35,
                19,
                11,
                10,
                18,
                22,
                10,
                26,
                14,
                54,
                8,
                4,
                4,
                4,
                1,
                5,
                2,
                1
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5404\u7701\u4eba\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -60,
                "margin": 8,
                "fontSize": 7
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u6c5f\u82cf",
                "\u6cb3\u5317",
                "\u6e56\u5357",
                "\u4e0a\u6d77",
                "\u5e7f\u4e1c",
                "\u9ed1\u9f99\u6c5f",
                "\u5409\u6797",
                "\u5c71\u897f",
                "\u9655\u897f",
                "\u5317\u4eac",
                "\u6d59\u6c5f",
                "\u56db\u5ddd",
                "\u5929\u6d25",
                "\u6cd5\u56fd",
                "\u6c5f\u897f",
                "\u8fbd\u5b81",
                "\u6cb3\u5357",
                "\u798f\u5efa",
                "\u5c71\u4e1c",
                "\u7518\u8083",
                "\u5b89\u5fbd",
                "\u6e56\u5317",
                "\u6d77\u5357",
                "\u4e2d\u56fd",
                "\u91cd\u5e86",
                "\u65b0\u7586",
                "\u5e7f\u897f",
                "\u8d35\u5dde",
                "\u9a6c\u6765\u897f\u4e9a",
                "\u4e2d\u56fd\u53f0\u6e7e",
                "\u4e2d\u56fd\u9999\u6e2f",
                "\u6fb3\u5927\u5229\u4e9a",
                "\u82f1\u56fd",
                "\u4e2d\u56fd\u6fb3\u95e8",
                "\u9752\u6d77",
                "\u5b81\u590f",
                "\u65b0\u52a0\u5761",
                "\u5185\u8499\u53e4",
                "\u7f8e\u56fd",
                "\u4e91\u5357",
                "\u65e5\u672c",
                "",
                "\u8377\u5170",
                "\u5370\u5ea6\u5c3c\u897f\u4e9a",
                "\u897f\u85cf",
                "\u97e9\u56fd",
                "\u897f\u73ed\u7259",
                "\u52a0\u62ff\u5927"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_616146a9926b4ce3bc2ff1bbb975b5e4.setOption(option_616146a9926b4ce3bc2ff1bbb975b5e4);
    </script>
</body>
</html>
