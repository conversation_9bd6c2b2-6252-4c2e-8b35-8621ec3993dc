<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>page</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

</head>
<body >
    <div id="3bb4c1dc96aa48ba82bf2239bb71532a" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_3bb4c1dc96aa48ba82bf2239bb71532a = echarts.init(
            document.getElementById('3bb4c1dc96aa48ba82bf2239bb71532a'), 'white', {renderer: 'canvas'});
        var option_3bb4c1dc96aa48ba82bf2239bb71532a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "pie",
            "colorBy": "data",
            "legendHoverLink": true,
            "selectedMode": false,
            "selectedOffset": 10,
            "clockwise": true,
            "startAngle": 90,
            "minAngle": 0,
            "minShowLabelAngle": 0,
            "avoidLabelOverlap": true,
            "stillShowZeroSum": true,
            "percentPrecision": 2,
            "showEmptyCircle": true,
            "emptyCircleStyle": {
                "color": "lightgray",
                "borderColor": "#000",
                "borderWidth": 0,
                "borderType": "solid",
                "borderDashOffset": 0,
                "borderCap": "butt",
                "borderJoin": "bevel",
                "borderMiterLimit": 10,
                "opacity": 1
            },
            "data": [
                {
                    "name": "happiness",
                    "value": 1264
                },
                {
                    "name": "like",
                    "value": 2167
                },
                {
                    "name": "anger",
                    "value": 908
                },
                {
                    "name": "surprise",
                    "value": 604
                },
                {
                    "name": "disgust",
                    "value": 234
                },
                {
                    "name": "sadness",
                    "value": 765
                },
                {
                    "name": "fear",
                    "value": 60
                }
            ],
            "radius": [
                "40%",
                "55%"
            ],
            "center": [
                "50%",
                "50%"
            ],
            "label": {
                "show": true,
                "position": "outside",
                "margin": 8,
                "formatter": "\n{hr|}\n {b|{b}: }{c}  {per|{d}%}  ",
                "backgroundColor": "#eee",
                "borderColor": "#aaa",
                "borderWidth": 1,
                "borderRadius": 4,
                "rich": {
                    "a": {
                        "color": "#999",
                        "lineHeight": 22,
                        "align": "center"
                    },
                    "abg": {
                        "backgroundColor": "#e3e3e3",
                        "width": "100%",
                        "align": "right",
                        "height": 22,
                        "borderRadius": [
                            4,
                            4,
                            0,
                            0
                        ]
                    },
                    "hr": {
                        "borderColor": "#aaa",
                        "width": "100%",
                        "borderWidth": 0.5,
                        "height": 0
                    },
                    "b": {
                        "fontSize": 16,
                        "lineHeight": 33
                    },
                    "per": {
                        "color": "#eee",
                        "backgroundColor": "#334455",
                        "padding": [
                            2,
                            4
                        ],
                        "borderRadius": 2
                    }
                }
            },
            "labelLine": {
                "show": true,
                "showAbove": false,
                "length": 15,
                "length2": 15,
                "smooth": false,
                "minTurnAngle": 90,
                "maxSurfaceAngle": 90
            }
        }
    ],
    "legend": [
        {
            "data": [
                "happiness",
                "like",
                "anger",
                "surprise",
                "disgust",
                "sadness",
                "fear"
            ],
            "selected": {}
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    }
};
        chart_3bb4c1dc96aa48ba82bf2239bb71532a.setOption(option_3bb4c1dc96aa48ba82bf2239bb71532a);
    </script>
</body>
</html>
