name: dmf
channels:
  - pytorch
  - conda-forge
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/msys2/
  - defaults
dependencies:
  - absl-py=0.15.0=pyhd3eb1b0_0
  - aiohttp=3.8.1=py37h2bbff1b_1
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - altgraph=0.17=pyhd3eb1b0_0
  - async-timeout=4.0.1=pyhd3eb1b0_0
  - asynctest=0.13.0=py_0
  - attrs=21.4.0=pyhd3eb1b0_0
  - blas=1.0=mkl
  - blinker=1.4=py37haa95532_0
  - brotlipy=0.7.0=py37h2bbff1b_1003
  - ca-certificates=2022.9.24=h5b45459_0
  - cachetools=4.2.2=pyhd3eb1b0_0
  - certifi=2022.9.24=pyhd8ed1ab_0
  - cffi=1.15.0=py37h2bbff1b_1
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.0.4=py37haa95532_0
  - colorama=0.4.4=pyhd3eb1b0_0
  - conda-pack=0.7.0=pyh6c4a22f_0
  - cryptography=37.0.1=py37h21b164f_0
  - cudatoolkit=10.1.243=h74a9793_0
  - dataclasses=0.8=pyh6d0b6a4_7
  - freetype=2.10.4=hd328e21_0
  - frozenlist=1.2.0=py37h2bbff1b_0
  - future=0.18.2=py37_1
  - google-auth=1.33.0=pyhd3eb1b0_0
  - google-auth-oauthlib=0.4.1=py_2
  - grpcio=1.42.0=py37hc60d5dd_0
  - idna=3.3=pyhd3eb1b0_0
  - importlib-metadata=4.11.3=py37haa95532_0
  - intel-openmp=2022.0.0=haa95532_3663
  - joblib=1.1.0=pyhd3eb1b0_0
  - jpeg=8d=0
  - libblas=3.9.0=15_win64_mkl
  - libcblas=3.9.0=15_win64_mkl
  - liblapack=3.9.0=15_win64_mkl
  - libpng=1.6.37=h2a8f88b_0
  - libprotobuf=3.20.1=h23ce68f_0
  - libtiff=4.2.0=he0120a3_1
  - libuv=1.40.0=he774522_0
  - libwebp=1.2.2=h2bbff1b_0
  - lz4-c=1.9.3=h2bbff1b_1
  - m2w64-gcc-libgfortran=5.3.0=6
  - m2w64-gcc-libs=5.3.0=7
  - m2w64-gcc-libs-core=5.3.0=7
  - m2w64-gmp=6.1.0=2
  - m2w64-libwinpthread-git=5.0.0.4634.697f757=2
  - macholib=1.14=pyhd3eb1b0_1
  - markdown=3.3.4=py37haa95532_0
  - mkl=2022.1.0=h6a75c08_874
  - msys2-conda-epoch=20160418=1
  - multidict=5.1.0=py37h2bbff1b_2
  - numpy=1.21.6=py37h2830a78_0
  - oauthlib=3.0.0=py37_0
  - openssl=1.1.1s=h2bbff1b_0
  - pefile=2019.4.18=py_0
  - pillow=9.0.1=py37hdc2b20a_0
  - pip=21.2.4=py37haa95532_0
  - protobuf=3.20.1=py37hd77b12b_0
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pycryptodome=3.15.0=py37h2bbff1b_0
  - pyinstaller=4.8=py37h8cc25b3_0
  - pyjwt=2.4.0=pyhd8ed1ab_0
  - pyopenssl=22.0.0=pyhd3eb1b0_0
  - pysocks=1.7.1=py37_1
  - python=3.7.13=h6244533_0
  - python_abi=3.7=2_cp37m
  - pytorch=1.11.0=py3.7_cpu_0
  - pytorch-mutex=1.0=cpu
  - pywin32=302=py37h2bbff1b_2
  - pywin32-ctypes=0.2.0=py37_1001
  - requests=2.27.1=pyhd3eb1b0_0
  - requests-oauthlib=1.3.0=py_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - scikit-learn=1.0.2=py37hf11a4ad_1
  - scipy=1.7.3=py37hb6553fb_0
  - setuptools=61.2.0=py37haa95532_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.38.3=h2bbff1b_0
  - tbb=2021.5.0=h59b6b97_0
  - tensorboard=2.9.0=pyhd8ed1ab_0
  - tensorboard-data-server=0.6.0=py37haa95532_0
  - tensorboard-plugin-wit=1.6.0=py_0
  - tensorboardx=2.2=pyhd3eb1b0_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - tk=8.6.12=h2bbff1b_0
  - torchvision=0.12.0=py37_cpu
  - tqdm=4.64.0=py37haa95532_0
  - typing-extensions=4.1.1=hd3eb1b0_0
  - typing_extensions=4.1.1=pyh06a4308_0
  - urllib3=1.26.9=py37haa95532_0
  - vc=14.2=h21ff451_1
  - vs2015_runtime=14.27.29016=h5e58377_2
  - werkzeug=2.0.3=pyhd3eb1b0_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - win_inet_pton=1.1.0=py37haa95532_0
  - wincertstore=0.2=py37haa95532_2
  - xz=5.2.5=h8cc25b3_1
  - yarl=1.6.3=py37h2bbff1b_0
  - zipp=3.8.0=py37haa95532_0
  - zlib=1.2.12=h8cc25b3_2
  - zstd=1.5.2=h19a0ad4_0
prefix: D:\appinstall\anaconda\envs\dmf
