# 修改后台logo
LOGO = ''
STATE = 1  # 启用自定义菜单配置
projectname = '智能舆情监测'

site_header = projectname  # 设置header
site_title = projectname  # 设置title
index_title = projectname

# 设置默认主题，指向主题css文件名。Admin Lte风格
THEME1 = 'admin.lte.css'

# 设置默认主题，指向主题css文件名。Element-ui风格
THEME2 = 'element.css'

# 设置默认主题，指向主题css文件名。layui风格
THEME3 = 'layui.css'

# 设置默认主题，指向主题css文件名。紫色风格
THEME4 = 'purple.css'

CONFIG = {
    # 是否使用系统默认菜单，自定义菜单时建议关闭。
    'system_keep': False,

    # 用于菜单排序和过滤, 不填此字段为默认排序和全部显示。空列表[] 为全部不显示.
    'menu_display': ['数据管理', '用户管理', '权限管理', '系统管理'],

    # 设置是否开启动态菜单, 默认为False. 如果开启, 则会在每次用户登陆时刷新展示菜单内容。
    # 一般建议关闭。
    'dynamic': True,
    'menus': [
        {
            'name': '数据管理',
            'icon': 'fa fa-th-list',
            'models': [
                {
                    'name': '微博热搜数据',
                    'url': '/admin/sysuser/weibohotsearch/',
                    'icon': 'fa fa-fire'
                },
                {
                    'name': '微博搜索数据',
                    'url': '/admin/sysuser/weibosearch/',
                    'icon': 'fa fa-search'
                },
            ],
        },
        {
            'name': '情感分析',
            'icon': 'fa fa-heart',
            'models': [
                {
                    'name': '微博情感分析系统',
                    'url': '/sentiment-app/',
                    'icon': 'fa fa-brain'
                },
                {
                    'name': '舆情预测分析',
                    'url': '/predict/',
                    'icon': 'fa fa-chart-line'
                },
            ],
        },
        {
            'name': '用户管理',
            'icon': 'fa fa-th-list',
            'models': [
                {
                    'name': '用户列表',
                    # 注意url按'/admin/应用名小写/模型名小写/'命名。
                    'url': '/admin/sysuser/user/',
                    'icon': 'fa fa-tasks'
                },
                # {
                #     'name': '待开发',
                #     # 注意url按'/admin/应用名小写/模型名小写/'命名。
                #     'url': '/admin/sysuser/info/',
                #     'icon': 'fa fa-tasks'
                # },
            ],
        },
        {
            'app': 'auth',
            'name': '权限管理',
            'icon': 'fas fa-user-shield',
            'models': [
                # {
                #     'name': '管理员',
                #     'icon': 'fa fa-user',
                #     'url': 'auth/user/'
                # },
                {
                    'name': '权限管理',
                    'icon': 'fa fa-th-list',
                    'url': 'auth/group/'
                }
            ]
        },
        {
            'app': 'sysuser',
            'name': '系统管理',
            'icon': 'fas fa-cog',
            'models': [
                {
                    'name': 'Session管理',
                    'icon': 'fa fa-tasks',
                    'url': '/admin/sessions/session/'
                },
                {
                    'name': '日志管理',
                    'icon': 'fa fa-tasks',
                    'url': '/admin/admin/logentry/'
                },
                {
                    'name': '内容类型',
                    'url': '/admin/contenttypes/contenttype/',
                    'icon': 'fa fa-list'
                },
                {
                    'name': '验证码',
                    'url': '/admin/captcha/captchastore/',
                    'icon': 'fa fa-key'
                },
                {
                    'name': '权限',
                    'url': '/admin/auth/permission/',
                    'icon': 'fa fa-key'
                },
            ]
        }

    ]
}

# 首页的快捷操作
SIMPLEUI_HOME_QUICK = False
# 首页的最近动作
SIMPLEUI_HOME_ACTION = False

# #后台首页设置
# 路由
# URL = '/bi'
# 标题
# TITLE = '数据大屏'
# ICON = 'fa fa-eye'

# 验证码设置
CAPTCHA_FONT_SIZE = 30
CAPTCHA_LETTER_ROTATION = (-10, 10)
CAPTCHA_BACKGROUND_COLOR = '#ffffff'
CAPTCHA_FOREGROUND_COLOR = '#000000'
