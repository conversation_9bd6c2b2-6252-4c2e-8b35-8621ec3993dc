
<!DOCTYPE html>
<html lang="en">
{% load static %}
<head>
    <!-- Title -->

    {% block title %} <title>首页</title>{% endblock title %}

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    {% block sjs %}
        <!-- Favicon -->
        <link rel="shortcut icon" href="{% static 'public/img/favicon.ico' %}">


        <!-- DEMO CHARTS -->
        <link rel="stylesheet" href="{% static 'public/demo/chartist.css' %}">
        <link rel="stylesheet" href="{% static 'public/demo/chartist-plugin-tooltip.cs' %}s">

        <!-- Template -->
        <link rel="stylesheet" href="{% static 'public/graindashboard/css/graindashboard.css' %}">
    {% endblock sjs %}
    <style>
        *{margin:0;padding:0;border-width:0;border-style:solid;box-sizing:border-box;border-color:var(--x-default-border-color,#E5E7EB)}
        .flex-nowrap{flex-wrap:nowrap}
        .p-0{padding:0}
        .w-100{width:100px}
        .px-md-3{padding-right:undefined}
        .ml-auto{margin-left:auto}
        .mx-3{margin-left:3px;margin-right:3px}
        .ml-2{margin-left:2px}
        .pt-2{padding-top:2px}
        .pb-1{padding-bottom:1px}
        .mt-4{margin-top:4px}
        .text-nowrap{color:rgb(undefined)}
        .mr-3{margin-right:3px}
        .mb-0{margin-bottom:0}

        /* 全局链接宽度限制 */
        .side-nav-menu-link {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .unfold-link {
            max-width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .navbar-brand {
            max-width: 150px;
            overflow: hidden;
        }

        .header-invoker {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .mr-2{margin-right:2px}
        .mr-1{margin-right:1px}
    </style>
</head>

<body class="has-sidebar has-fixed-sidebar-and-header">
<!-- Header -->
<header class="header bg-body">
    <nav class="navbar flex-nowrap p-0">
        <div class="navbar-brand-wrapper d-flex align-items-center col-auto">
            <!-- Logo For Mobile View -->
            <a class="navbar-brand navbar-brand-mobile" href="/">
                <img class="img-fluid w-100" src="{% static 'public/img/logo-mini.png' %}" alt="Graindashboard">
            </a>
            <!-- End Logo For Mobile View -->

            <!-- Logo For Desktop View -->
            <a class="navbar-brand navbar-brand-desktop" href="/">
                <img class="side-nav-show-on-closed" src="{% static 'public/img/logo-mini.png' %}" alt="Graindashboard"
                     style="width: auto; height: 33px;">
                <img class="side-nav-hide-on-closed" src="{% static 'public/img/logo.png' %}" alt="Graindashboard"
                     style="width: auto; height: 33px;">
            </a>
            <!-- End Logo For Desktop View -->
        </div>

        <div class="header-content col px-md-3">
            <div class="d-flex align-items-center">
                <!-- Side Nav Toggle -->
                <a class="js-side-nav header-invoker d-flex mr-md-2" href="#"
                   data-close-invoker="#sidebarClose"
                   data-target="#sidebar"
                   data-target-wrapper="body">
                    <i class="gd-align-left"></i>
                </a>
                <!-- End Side Nav Toggle -->

                <!-- 情感分析系统快捷按钮 -->
                <div class="ml-auto mr-3">
                    <a href="http://localhost:8501/" target="_blank"
                       class="btn btn-sm btn-outline-primary d-flex align-items-center"
                       style="border-radius: 20px; padding: 6px 12px; font-size: 12px; text-decoration: none;">
                        <i class="gd-rocket mr-1"></i>
                        <span class="d-none d-md-inline">情感分析系统</span>
                        <span class="d-md-none">分析</span>
                    </a>
                </div>

                <!-- User Notifications -->
                <div class="dropdown">
                    <a id="notificationsInvoker" class="header-invoker" href="#" aria-controls="notifications"
                       aria-haspopup="true" aria-expanded="false" data-unfold-event="click"
                       data-unfold-target="#notifications" data-unfold-type="css-animation" data-unfold-duration="300"
                       data-unfold-animation-in="fadeIn" data-unfold-animation-out="fadeOut">
                    </a>


                </div>
                <!-- End User Notifications -->
                <!-- User Avatar -->
                <div class="dropdown mx-3 dropdown ml-2">
                    <a id="profileMenuInvoker" class="header-complex-invoker" href="#" aria-controls="profileMenu"
                       aria-haspopup="true" aria-expanded="false" data-unfold-event="click"
                       data-unfold-target="#profileMenu" data-unfold-type="css-animation" data-unfold-duration="300"
                       data-unfold-animation-in="fadeIn" data-unfold-animation-out="fadeOut">
                        <!--img class="avatar rounded-circle mr-md-2" src="#" alt="John Doe"-->
                        <img src="








                                {% if request.user.touxiang %}{{ request.user.touxiang.url }}{% else %}{% static 'public/img/logo-mini.png' %}{% endif %}"
                             style="height: 50px">
                        {#                        <span class="d-none d-md-block">{% if request.user.is_authenticated %}#}
                        {#                            {{ request.user.username }}{% else %}未登录{% endif %}</span>#}
                        <i class="gd-angle-down d-none d-md-block ml-2"></i>
                    </a>
                    {% if request.user.is_authenticated %}
                        <ul id="profileMenu"
                            class="unfold unfold-user unfold-light unfold-top unfold-centered position-absolute pt-2 pb-1 mt-4 unfold-css-animation unfold-hidden fadeOut"
                            aria-labelledby="profileMenuInvoker" style="animation-duration: 300ms;">
                            <li class="unfold-item">
                                <a class="unfold-link d-flex align-items-center text-nowrap"
                                   href="{% url 'sysuser:account' %}">
                    <span class="unfold-item-icon mr-3">
                      <i class="gd-user"></i>
                    </span>
                                    个人信息
                                </a>
                            </li>
                            <li class="unfold-item unfold-item-has-divider">
                                <a class="unfold-link d-flex align-items-center text-nowrap"
                                   href="/admin/">
                    <span class="unfold-item-icon mr-3">
                      <i class="gd-power-off"></i>
                    </span>
                                    后台管理
                                </a>
                            </li>
                            <li class="unfold-item unfold-item-has-divider">
                                <a class="unfold-link d-flex align-items-center text-nowrap"
                                   href="{% url 'sysuser:sysuser_logout' %}">
                    <span class="unfold-item-icon mr-3">
                      <i class="gd-power-off"></i>
                    </span>
                                    注销
                                </a>
                            </li>
                        </ul>
                    {% else %}
                        <ul id="profileMenu"
                            class="unfold unfold-user unfold-light unfold-top unfold-centered position-absolute pt-2 pb-1 mt-4 unfold-css-animation unfold-hidden fadeOut"
                            aria-labelledby="profileMenuInvoker" style="animation-duration: 300ms;">
                            <li class="unfold-item">
                                <a class="unfold-link d-flex align-items-center text-nowrap"
                                   href="{% url 'sysuser:sysuser_login' %}">
                    <span class="unfold-item-icon mr-3">
                      <i class="gd-user"></i>
                    </span>
                                    登录
                                </a>
                            </li>
                            <li class="unfold-item unfold-item-has-divider">
                                <a class="unfold-link d-flex align-items-center text-nowrap"
                                   href="{% url 'sysuser:sysuser_register' %}">
                    <span class="unfold-item-icon mr-3">
                      <i class="gd-power-off"></i>
                    </span>
                                    注册
                                </a>
                            </li>
                        </ul>
                    {% endif %}
                </div>
                <!-- End User Avatar -->
            </div>
        </div>
    </nav>
</header>
<!-- End Header -->
<main class="main">
    <!-- Sidebar Nav -->
    <aside id="sidebar" class="js-custom-scroll side-nav">
        <ul id="sideNav" class="side-nav-menu side-nav-menu-top-level mb-0">
            <!-- Title -->
            <li class="sidebar-heading h6">数据分析</li>
            <!-- End Title -->

            <!-- Dashboard -->
            <li class="side-nav-menu-item active">
                <a class="side-nav-menu-link media align-items-center" href="{% url 'appcenter:index' %}">
              <span class="side-nav-menu-icon d-flex mr-3">
                <i class="gd-dashboard"></i>
              </span>
                    <span class="side-nav-fadeout-on-closed media-body">智能舆情监测</span>
                </a>
            </li>
            <!-- End Dashboard -->


            <!-- Title -->
            <li class="sidebar-heading h6">功能</li>
            <!-- End Title -->

            <!-- Users -->
            <li class="side-nav-menu-item side-nav-has-menu">
                <a class="side-nav-menu-link media align-items-center" href="#"
                   data-target="#subUsers">
                  <span class="side-nav-menu-icon d-flex mr-3">
                    <i class="gd-user"></i>
                  </span>
                    <span class="side-nav-fadeout-on-closed media-body">功能中心</span>
                    <span class="side-nav-control-icon d-flex">
                <i class="gd-angle-right side-nav-fadeout-on-closed"></i>
              </span>
                    <span class="side-nav__indicator side-nav-fadeout-on-closed"></span>
                </a>

                <!-- Users: subUsers -->
                <ul id="subUsers" class="side-nav-menu side-nav-menu-second-level mb-0">
                    <li class="side-nav-menu-item">
                        <a class="side-nav-menu-link" href="{% url 'appcenter:table1' %}">热搜查看</a>
                    </li>
                    <li class="side-nav-menu-item">
                        <a class="side-nav-menu-link" href="{% url 'appcenter:table2' %}">微博帖子查看</a>
                    </li>
                    <li class="side-nav-menu-item">
                        <a class="side-nav-menu-link" href="{% url 'appcenter:bi' %}">数据大屏</a>
                    </li>
                    <li class="side-nav-menu-item">
                        <a class="side-nav-menu-link" href="http://localhost:8501/" target="_blank">
                        </i>舆情分析
                        </a>
                    </li>
                </ul>
                <!-- End Users: subUsers -->
            </li>
            <!-- End Users -->


        </ul>
    </aside>
    <!-- End Sidebar Nav -->

    {% block content %}
    {% endblock content %}
</main>
{% block endjs %}
    <script src="{% static 'public/graindashboard/js/graindashboard.js' %}"></script>
    <script src="{% static 'public/graindashboard/js/graindashboard.vendor.js' %}"></script>

    <!-- DEMO CHARTS -->
    <script src="{% static 'public/demo/resizeSensor.js' %}"></script>


    <script>
        // 检查组件是否存在再初始化
        if (typeof $.GDCore !== 'undefined' && $.GDCore.components) {
            if ($.GDCore.components.GDChartistArea && $('.js-area-chart').length > 0) {
                $.GDCore.components.GDChartistArea.init('.js-area-chart');
            }
            if ($.GDCore.components.GDChartistBar && $('.js-bar-chart').length > 0) {
                $.GDCore.components.GDChartistBar.init('.js-bar-chart');
            }
            if ($.GDCore.components.GDChartistDonut && $('.js-donut-chart').length > 0) {
                $.GDCore.components.GDChartistDonut.init('.js-donut-chart');
            }
        }
    </script>
{% endblock endjs %}
</body>
</html>
