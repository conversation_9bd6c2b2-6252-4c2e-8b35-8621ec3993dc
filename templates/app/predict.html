{% extends 'template/index.html' %}
{% load static %}

{% block sjs %}
    {{ block.super }}
    <!-- ECharts 图表库 -->
    <script src="{% static 'visual/assets/V5/echarts.min.js' %}"></script>
    <script src="{% static 'visual/assets/V5/themes/macarons.js' %}"></script>
{% endblock sjs %}

{% block content %}
    <style>
        /* 卡片样式 */
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
            padding: 20px;
            background-color: #fff;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;
            padding: 15px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .card-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }

        .card-content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .form-control-file {
            margin-top: 5px;
        }

        .form-text {
            font-size: 12px;
            color: #6c757d;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            padding: 10px 20px;
            font-size: 14px;
            color: #fff;
            border-radius: 4px;
            text-transform: uppercase;
            cursor: pointer;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            padding: 10px 20px;
            font-size: 14px;
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-outline-primary {
            background-color: transparent;
            border-color: #007bff;
            color: #007bff;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-outline-success {
            background-color: transparent;
            border-color: #28a745;
            color: #28a745;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-group {
            display: flex;
            gap: 10px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-success {
            color: #fff;
            background-color: #28a745;
        }

        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }

        .badge-secondary {
            color: #fff;
            background-color: #6c757d;
        }

        .badge-warning {
            color: #212529;
            background-color: #ffc107;
        }

        .text-success {
            color: #28a745;
        }

        .text-warning {
            color: #ffc107;
        }

        .text-danger {
            color: #dc3545;
        }

        .text-center {
            text-align: center;
        }

        .mt-4 {
            margin-top: 1.5rem;
        }

        .mt-3 {
            margin-top: 1rem;
        }

        .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
        }

        .col-md-3, .col-md-4, .col-md-6 {
            position: relative;
            width: 100%;
            padding-right: 15px;
            padding-left: 15px;
        }

        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .card-body {
            padding: 1.25rem;
        }
    </style>

    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">舆情分析</h4>
                    </div>
                    <div class="card-content">
                        <!-- 分析模式选择 -->
                        <div class="form-group">
                            <label>选择分析模式</label>
                            <div class="btn-group" role="group" style="margin-bottom: 20px;">
                                <button type="button" class="btn btn-outline-primary" id="textModeBtn" onclick="switchMode('text')">文本分析</button>
                                <button type="button" class="btn btn-outline-success" id="weiboModeBtn" onclick="switchMode('weibo')">微博热搜分析</button>
                            </div>
                        </div>

                        <!-- 文本分析模式 -->
                        <div id="textMode">
                            <form method="post" action="">
                                {% csrf_token %}
                                <input type="hidden" name="mode" value="text">

                                <!-- 分析方法选择 -->
                                <div class="form-group">
                                    <label>选择分析方法</label>
                                    <div class="btn-group" role="group" style="margin-bottom: 15px;">
                                        <input type="radio" name="analysis_method" value="textcnn" id="textcnn" {% if not analysis_method or analysis_method == 'textcnn' %}checked{% endif %}>
                                        <label for="textcnn" class="btn btn-outline-primary">TextCNN深度学习</label>

                                        <input type="radio" name="analysis_method" value="snownlp" id="snownlp" {% if analysis_method == 'snownlp' %}checked{% endif %}>
                                        <label for="snownlp" class="btn btn-outline-success">SnowNLP统计模型</label>
                                    </div>
                                    <small class="form-text text-muted">
                                        TextCNN：支持7种情感分类（喜欢、厌恶、开心、难过、愤怒、惊讶、害怕），准确率更高<br>
                                        SnowNLP：支持积极、消极、中性三分类，处理速度快
                                    </small>
                                </div>

                                <div class="form-group">
                                    <label for="textInput">输入文本进行情感分析</label>
                                    <textarea class="form-control" name="text" id="textInput" rows="5"
                                              placeholder="请输入中文文本，例如：今天天气真好，心情不错！">{% if text and mode == 'text' %}{{ text }}{% endif %}</textarea>
                                    <small class="form-text text-muted">支持中文文本的情感分析，将分析文本的情感倾向。</small>
                                </div>
                                <button type="submit" class="btn btn-primary">分析情感</button>
                            </form>
                        </div>

                        <!-- 微博热搜分析模式 -->
                        <div id="weiboMode" style="display: none;">
                            <form method="post" action="">
                                {% csrf_token %}
                                <input type="hidden" name="mode" value="weibo">

                                <div class="form-group">
                                    <label for="analysisTypeSelect">选择分析类型</label>
                                    <select class="form-control" name="analysis_type" id="analysisTypeSelect" onchange="toggleWeiboSelect()">
                                        <option value="all">全部文章</option>
                                        <option value="single">单条微博分析</option>
                                    </select>
                                    <small class="form-text text-muted">选择全部文章分析或单条微博进行分析。</small>
                                </div>

                                <div class="form-group" id="weiboSelectGroup" style="display: none;">
                                    <label for="weiboSelect">选择要分析的微博</label>
                                    <select class="form-control" name="selected_weibo_id" id="weiboSelect">
                                        <option value="">请选择一条微博</option>
                                        {% for weibo in weibo_data %}
                                        <option value="{{ weibo.id }}">
                                            {{ weibo.author_nickname }} - {{ weibo.content|truncatechars:50 }} ({{ weibo.publish_time|date:"m-d H:i" }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <small class="form-text text-muted">选择一条具体的微博进行详细分析。</small>
                                </div>

                                <button type="submit" class="btn btn-success">开始分析</button>
                            </form>
                        </div>

                        <!-- 文本分析结果 -->
                        {% if text and mode == 'text' %}
                        <div class="mt-4">
                            <h5>文本分析结果：</h5>
                            <div class="alert alert-success">
                                <p><strong>输入文本：</strong>{{ text }}</p>
                                <p><strong>分析模型：</strong>{{ analysis_model|default:"默认模型" }}</p>
                                {% if emotion and emotion_chinese %}
                                <p><strong>情感类别：</strong>
                                    <span class="badge {% if emotion in 'happiness,like' %}badge-success{% elif emotion in 'sadness,anger,disgust,fear' %}badge-danger{% elif emotion == 'surprise' %}badge-warning{% else %}badge-secondary{% endif %}">
                                        {{ emotion_chinese }}
                                    </span>
                                    <small class="text-muted">({{ emotion }})</small>
                                </p>
                                <p><strong>置信度：</strong>{{ confidence|floatformat:4 }} ({{ confidence|floatformat:2 }}%)</p>
                                {% else %}
                                <p><strong>情感分数：</strong>{{ sentiment_score|floatformat:4 }} (0-1之间，越接近1越积极)</p>
                                <p><strong>情感类别：</strong>
                                    <span class="badge {% if sentiment_category == '积极' %}badge-success{% elif sentiment_category == '消极' %}badge-danger{% else %}badge-secondary{% endif %}">
                                        {{ sentiment_category }}
                                    </span>
                                </p>
                                {% endif %}
                            </div>

                            <!-- 文本分析图表展示 -->
                            <div class="row mt-4">
                                <!-- 情感分类饼图 -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">📊 情感分类图</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if pie_chart %}
                                                <div style="height: 400px;">
                                                    {{ pie_chart|safe }}
                                                </div>
                                            {% else %}
                                                <div class="text-center text-muted" style="height: 400px; display: flex; align-items: center; justify-content: center;">
                                                    <p>暂无图表数据</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- 情感倾向分析柱状图 -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">📈 情感倾向分析</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if bar_chart %}
                                                <div style="height: 400px;">
                                                    {{ bar_chart|safe }}
                                                </div>
                                            {% else %}
                                                <div class="text-center text-muted" style="height: 400px; display: flex; align-items: center; justify-content: center;">
                                                    <p>暂无图表数据</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 情感分数仪表盘 -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">🎯 情感分数仪表盘</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if gauge_chart %}
                                                <div style="height: 400px;">
                                                    {{ gauge_chart|safe }}
                                                </div>
                                            {% else %}
                                                <div class="text-center text-muted" style="height: 400px; display: flex; align-items: center; justify-content: center;">
                                                    <p>暂无图表数据</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 微博热搜分析结果 -->
                        {% if mode == 'weibo' and weibo_analysis %}
                        <div class="mt-4">
                            <h5>微博热搜舆情分析结果：</h5>
                            <div class="alert alert-info">
                                <div class="row">
                                    <div class="col-md-3">
                                        <p><strong>分析文章数：</strong>{{ weibo_analysis.total_count }}条</p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>整体情感倾向：</strong>
                                            <span class="badge {% if weibo_analysis.overall_sentiment == '积极' %}badge-success{% elif weibo_analysis.overall_sentiment == '消极' %}badge-danger{% else %}badge-warning{% endif %}">
                                                {{ weibo_analysis.overall_sentiment }}
                                            </span>
                                        </p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>平均情感分数：</strong>{{ weibo_analysis.avg_sentiment|floatformat:3 }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <p><strong>分析时间：</strong>{{ weibo_analysis.analysis_time }}</p>
                                    </div>
                                </div>

                                <!-- 添加随机分析评论 -->
                                {% if weibo_analysis.analysis_comment %}
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <p><strong>分析说明：</strong>{{ weibo_analysis.analysis_comment }}</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>

                            <!-- 情感分布统计 -->
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-success">积极情感</h5>
                                            <h2 class="text-success">{{ weibo_analysis.positive_count }}</h2>
                                            <p class="card-text">{{ weibo_analysis.positive_percent|floatformat:1 }}%</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-warning">中性情感</h5>
                                            <h2 class="text-warning">{{ weibo_analysis.neutral_count }}</h2>
                                            <p class="card-text">{{ weibo_analysis.neutral_percent|floatformat:1 }}%</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-danger">消极情感</h5>
                                            <h2 class="text-danger">{{ weibo_analysis.negative_count }}</h2>
                                            <p class="card-text">{{ weibo_analysis.negative_percent|floatformat:1 }}%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 图表展示区域 -->
                            <div class="row mt-4">
                                <!-- 情感分布饼图 -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">📊 情感分布图</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if pie_chart %}
                                                <div style="height: 400px;">
                                                    {{ pie_chart|safe }}
                                                </div>
                                            {% else %}
                                                <div class="text-center text-muted" style="height: 400px; display: flex; align-items: center; justify-content: center;">
                                                    <p>暂无图表数据</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- 热度与情感关系柱状图 -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">📈 热度与情感关系</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if bar_chart %}
                                                <div style="height: 400px;">
                                                    {{ bar_chart|safe }}
                                                </div>
                                            {% else %}
                                                <div class="text-center text-muted" style="height: 400px; display: flex; align-items: center; justify-content: center;">
                                                    <p>暂无图表数据</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 舆情趋势图 -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title">📈 舆情趋势分析</h5>
                                        </div>
                                        <div class="card-body">
                                            {% if weibo_trend_chart %}
                                                <div style="height: 400px;">
                                                    {{ weibo_trend_chart|safe }}
                                                </div>
                                            {% else %}
                                                <div class="text-center text-muted" style="height: 400px; display: flex; align-items: center; justify-content: center;">
                                                    <p>暂无图表数据</p>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模式切换功能
        function switchMode(mode) {
            const textMode = document.getElementById('textMode');
            const weiboMode = document.getElementById('weiboMode');
            const textModeBtn = document.getElementById('textModeBtn');
            const weiboModeBtn = document.getElementById('weiboModeBtn');

            if (mode === 'text') {
                textMode.style.display = 'block';
                weiboMode.style.display = 'none';
                textModeBtn.classList.remove('btn-outline-primary');
                textModeBtn.classList.add('btn-primary');
                weiboModeBtn.classList.remove('btn-success');
                weiboModeBtn.classList.add('btn-outline-success');
            } else if (mode === 'weibo') {
                textMode.style.display = 'none';
                weiboMode.style.display = 'block';
                textModeBtn.classList.remove('btn-primary');
                textModeBtn.classList.add('btn-outline-primary');
                weiboModeBtn.classList.remove('btn-outline-success');
                weiboModeBtn.classList.add('btn-success');
            }
        }

        // 控制微博选择框的显示
        function toggleWeiboSelect() {
            const analysisType = document.getElementById('analysisTypeSelect').value;
            const weiboSelectGroup = document.getElementById('weiboSelectGroup');

            if (analysisType === 'single') {
                weiboSelectGroup.style.display = 'block';
            } else {
                weiboSelectGroup.style.display = 'none';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            {% if mode == 'weibo' %}
                switchMode('weibo');
                // 设置分析类型的值
                {% if analysis_type %}
                    document.getElementById('analysisTypeSelect').value = '{{ analysis_type }}';
                {% endif %}
                toggleWeiboSelect();
                // 设置选中的微博
                {% if selected_weibo_id %}
                    document.getElementById('weiboSelect').value = '{{ selected_weibo_id }}';
                {% endif %}
            {% else %}
                switchMode('text');
            {% endif %}
        });
    </script>
{% endblock content %}
