<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .header-bar {
            background: rgba(255, 255, 255, 0.95);
            padding: 10px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 1000;
            position: relative;
        }
        
        .header-title {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }
        
        .header-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .btn-refresh {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn-refresh:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        
        .iframe-container {
            height: calc(100vh - 60px);
            width: 100%;
            border: none;
            background: white;
            position: relative;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
            z-index: 1000;
        }
        
        .error-message i {
            font-size: 48px;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .fullscreen-btn {
            background: transparent;
            border: 1px solid #667eea;
            color: #667eea;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .fullscreen-btn:hover {
            background: #667eea;
            color: white;
        }
        
        /* 全屏模式样式 */
        .fullscreen-mode {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: white;
        }
        
        .fullscreen-mode .header-bar {
            background: rgba(0, 0, 0, 0.8);
            color: white;
        }
        
        .fullscreen-mode .header-title {
            color: white;
        }
        
        .fullscreen-mode .iframe-container {
            height: calc(100vh - 50px);
        }
    </style>
</head>
<body>
    <!-- 顶部控制栏 -->
    <div class="header-bar">
        <h1 class="header-title">
            <i class="fas fa-brain"></i> {{ page_title }}
        </h1>
        <div class="header-controls">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>运行中</span>
            </div>
            <button class="fullscreen-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand"></i> 全屏
            </button>
            <button class="btn-refresh" onclick="refreshIframe()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>

    <!-- iframe容器 -->
    <div class="iframe-container" id="iframe-container">
        <!-- 加载遮罩 -->
        <div class="loading-overlay" id="loading-overlay">
            <div class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-3">正在加载情感分析系统...</p>
            </div>
        </div>
        
        <!-- iframe -->
        <iframe 
            id="sentiment-iframe" 
            src="{{ streamlit_url }}" 
            title="{{ page_title }}"
            onload="hideLoading()"
            onerror="showError()">
        </iframe>
        
        <!-- 错误提示 -->
        <div class="error-message" id="error-message" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <h4>无法加载应用</h4>
            <p>请确保 {{ streamlit_url }} 正在运行</p>
            <button class="btn-refresh" onclick="refreshIframe()">
                <i class="fas fa-redo"></i> 重试
            </button>
        </div>
    </div>

    <script>
        let isFullscreen = false;
        
        function hideLoading() {
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        }
        
        function showLoading() {
            const loadingOverlay = document.getElementById('loading-overlay');
            const errorMessage = document.getElementById('error-message');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
            if (errorMessage) {
                errorMessage.style.display = 'none';
            }
        }
        
        function showError() {
            const loadingOverlay = document.getElementById('loading-overlay');
            const errorMessage = document.getElementById('error-message');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
            if (errorMessage) {
                errorMessage.style.display = 'block';
            }
        }
        
        function refreshIframe() {
            showLoading();
            const iframe = document.getElementById('sentiment-iframe');
            if (iframe) {
                iframe.src = iframe.src;
            }
        }
        
        function toggleFullscreen() {
            const container = document.getElementById('iframe-container');
            const btn = document.querySelector('.fullscreen-btn');
            
            if (!isFullscreen) {
                // 进入全屏
                document.body.classList.add('fullscreen-mode');
                btn.innerHTML = '<i class="fas fa-compress"></i> 退出全屏';
                isFullscreen = true;
            } else {
                // 退出全屏
                document.body.classList.remove('fullscreen-mode');
                btn.innerHTML = '<i class="fas fa-expand"></i> 全屏';
                isFullscreen = false;
            }
        }
        
        // 监听ESC键退出全屏
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isFullscreen) {
                toggleFullscreen();
            }
        });
        
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', function() {
            setTimeout(hideLoading, 2000); // 2秒后隐藏加载动画
        });
        
        // 检查iframe是否加载成功
        setTimeout(function() {
            const iframe = document.getElementById('sentiment-iframe');
            try {
                // 尝试访问iframe内容，如果失败说明可能有跨域问题或服务未启动
                if (iframe.contentDocument === null) {
                    // 这是正常的跨域情况，隐藏加载动画
                    hideLoading();
                }
            } catch (e) {
                // 跨域访问被阻止，这是正常的，隐藏加载动画
                hideLoading();
            }
        }, 3000);
        
        // 定期检查服务状态
        function checkServiceStatus() {
            fetch('{{ streamlit_url }}', { mode: 'no-cors' })
                .then(() => {
                    // 服务正常
                    document.querySelector('.status-dot').style.backgroundColor = '#28a745';
                    document.querySelector('.status-indicator span').textContent = '运行中';
                })
                .catch(() => {
                    // 服务异常
                    document.querySelector('.status-dot').style.backgroundColor = '#dc3545';
                    document.querySelector('.status-indicator span').textContent = '连接失败';
                });
        }
        
        // 每30秒检查一次服务状态
        setInterval(checkServiceStatus, 30000);
        
        // 页面加载时检查一次
        checkServiceStatus();
    </script>
</body>
</html>
