<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微博情感分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 0 auto;
            padding: 30px;
            max-width: 1400px;
            min-height: calc(100vh - 40px);
        }

        .header-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .control-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .iframe-container {
            background: #ffffff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: calc(100vh - 300px);
            min-height: 600px;
        }

        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }

        .status-stopped {
            background-color: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }

        .btn-danger {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
        }

        .feature-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }

        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }

        .feature-list li {
            margin-bottom: 8px;
        }
        *{margin:0;padding:0;border-width:0;border-style:solid;box-sizing:border-box;border-color:var(--x-default-border-color,#E5E7EB)}
        .mb-0{margin-bottom:0}
        .text-end{color:rgb(undefined)}
        .h-100{height:100px}
        .text-center{text-align:center}
        .mb-4{margin-bottom:4px}
        .mt-3{margin-top:3px}
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="header-section">
            <h1><i class="fas fa-brain"></i> 微博情感分析系统</h1>
            <p class="mb-0">基于weibo-sent-analyse-master的完整情感分析解决方案</p>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5><i class="fas fa-cogs"></i> 应用控制</h5>
                    <p class="mb-0">
                        <span class="status-indicator {% if is_running %}status-running{% else %}status-stopped{% endif %}"></span>
                        状态: <strong id="app-status">{% if is_running %}运行中{% else %}已停止{% endif %}</strong>
                        {% if is_running %}
                        <small class="text-muted">(端口: {{ port }})</small>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <button id="start-btn" class="btn btn-success me-2" {% if is_running %}style="display:none"{% endif %}>
                        <i class="fas fa-play"></i> 启动应用
                    </button>
                    <button id="stop-btn" class="btn btn-danger me-2" {% if not is_running %}style="display:none"{% endif %}>
                        <i class="fas fa-stop"></i> 停止应用
                    </button>
                    <button id="refresh-btn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> 刷新状态
                    </button>
                </div>
            </div>
        </div>

        <!-- 应用iframe容器 -->
        <div class="iframe-container" id="iframe-container">
            {% if is_running and streamlit_url %}
            <iframe id="sentiment-iframe" src="{{ streamlit_url }}" title="微博情感分析应用"></iframe>
            {% else %}
            <div class="d-flex align-items-center justify-content-center h-100">
                <div class="text-center">
                    <i class="fas fa-rocket fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">应用未启动</h3>
                    <p class="text-muted">点击"启动应用"按钮来启动微博情感分析系统</p>
                    <button class="btn btn-primary btn-lg" onclick="startApp()">
                        <i class="fas fa-play"></i> 启动应用
                    </button>
                </div>
            </div>
            {% endif %}

            <!-- 加载遮罩 -->
            <div class="loading-overlay" id="loading-overlay" style="display: none;">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-3">正在启动应用，请稍候...</p>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="feature-list">
            <h5><i class="fas fa-info-circle"></i> 系统功能</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>情感分析功能</h6>
                    <ul>
                        <li>单句情感分析：输入文本即时分析情感倾向</li>
                        <li>批量数据分析：支持CSV文件和在线爬取</li>
                        <li>7种情感分类：喜欢、厌恶、开心、难过、愤怒、惊讶、害怕</li>
                        <li>基于TextCNN深度学习模型</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>可视化分析</h6>
                    <ul>
                        <li>情感分布饼状图和雷达图</li>
                        <li>情感-地区关系折线图</li>
                        <li>情感-点赞关系柱状图</li>
                        <li>热力地图和词云图</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- CSRF Token -->
    {% csrf_token %}

    <script>
        // CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;

        function showLoading() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function updateStatus(isRunning, url = null) {
            const statusIndicator = document.querySelector('.status-indicator');
            const statusText = document.getElementById('app-status');
            const startBtn = document.getElementById('start-btn');
            const stopBtn = document.getElementById('stop-btn');
            const iframe = document.getElementById('sentiment-iframe');
            const container = document.getElementById('iframe-container');

            if (isRunning) {
                statusIndicator.className = 'status-indicator status-running';
                statusText.textContent = '运行中';
                startBtn.style.display = 'none';
                stopBtn.style.display = 'inline-block';

                if (url && iframe) {
                    iframe.src = url;
                } else if (url) {
                    container.innerHTML = `<iframe id="sentiment-iframe" src="${url}" title="微博情感分析应用"></iframe>`;
                }
            } else {
                statusIndicator.className = 'status-indicator status-stopped';
                statusText.textContent = '已停止';
                startBtn.style.display = 'inline-block';
                stopBtn.style.display = 'none';

                container.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="fas fa-rocket fa-5x text-muted mb-4"></i>
                            <h3 class="text-muted">应用未启动</h3>
                            <p class="text-muted">点击"启动应用"按钮来启动微博情感分析系统</p>
                            <button class="btn btn-primary btn-lg" onclick="startApp()">
                                <i class="fas fa-play"></i> 启动应用
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        function startApp() {
            showLoading();

            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: 'action=start'
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    updateStatus(true, data.url);
                    // 延迟一下再加载iframe，确保Streamlit完全启动
                    setTimeout(() => {
                        if (data.url) {
                            const iframe = document.getElementById('sentiment-iframe');
                            if (iframe) iframe.src = data.url;
                        }
                    }, 2000);
                } else {
                    alert('启动失败: ' + data.message);
                }
            })
            .catch(error => {
                hideLoading();
                alert('启动失败: ' + error.message);
            });
        }

        function stopApp() {
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: 'action=stop'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatus(false);
                } else {
                    alert('停止失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('停止失败: ' + error.message);
            });
        }

        function refreshStatus() {
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: 'action=status'
            })
            .then(response => response.json())
            .then(data => {
                updateStatus(data.running, data.url);
            })
            .catch(error => {
                console.error('刷新状态失败:', error);
            });
        }

        // 绑定事件
        document.getElementById('start-btn').addEventListener('click', startApp);
        document.getElementById('stop-btn').addEventListener('click', stopApp);
        document.getElementById('refresh-btn').addEventListener('click', refreshStatus);

        // 定期检查状态
        setInterval(refreshStatus, 30000); // 每30秒检查一次
    </script>
</body>
</html>
