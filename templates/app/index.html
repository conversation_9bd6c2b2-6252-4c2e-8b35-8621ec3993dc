{% extends 'template/index.html' %}
{% load static %}

{% block sjs %}
    {{ block.super }}
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
        }
        .stats-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .chart-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-card:hover {
            transform: translateY(-2px);
        }
        .nav-v2 .nav-link {
            border-radius: 10px 10px 0 0;
            transition: all 0.3s ease;
            max-width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .nav-v2 .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .content-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }

        /* 链接宽度限制 */
        .function-link {
            display: block;
            max-width: 200px;
            margin: 0 auto;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .function-link:hover {
            text-decoration: none;
            transform: translateY(-2px);
        }

        .function-card {
            padding: 1.5rem;
            border-radius: 15px;
            transition: all 0.3s ease;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .function-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .function-card h6 {
            margin: 0.5rem 0;
            font-weight: 600;
        }

        .function-card small {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        /* 分页链接限制 */
        .pagination a {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 表格链接限制 */
        .table a {
            max-width: 150px;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 导航栏链接限制 */
        .navbar-nav .nav-link {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 按钮链接限制 */
        .btn {
            max-width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 微博内容HTML样式 */
        .content-card .text-muted {
            line-height: 1.6;
        }

        .content-card a {
            color: #1da1f2;
            text-decoration: none;
            max-width: 250px;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: top;
        }

        .content-card a:hover {
            color: #0d8bd9;
            text-decoration: underline;
        }

        .content-card img {
            max-width: 24px;
            max-height: 24px;
            vertical-align: middle;
            margin: 0 3px;
            border-radius: 3px;
        }

        .content-card br {
            line-height: 2;
        }
    </style>
{% endblock sjs %}

{% block content %}
    <div class="content">
        <div class="py-4 px-3 px-md-4">
            <!-- 页面头部 -->
            <div class="dashboard-header text-center">
                <h1 class="display-4 mb-3">🔥 微博舆情分析系统</h1>
                <p class="lead mb-0">实时监测热点话题，深度分析舆情趋势</p>
            </div>

            <!-- 数据统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <h3 class="mb-1">364</h3>
                            <p class="mb-0">热搜数据</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stats-card text-center" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <div class="card-body">
                            <h3 class="mb-1">18,055</h3>
                            <p class="mb-0">搜索数据</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stats-card text-center" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                        <div class="card-body">
                            <h3 class="mb-1">6</h3>
                            <p class="mb-0">数据分类</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stats-card text-center" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                        <div class="card-body">
                            <h3 class="mb-1">实时</h3>
                            <p class="mb-0">数据更新</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要图表区域 -->
            <div class="row">
                <div class="col-12">
                    <div class="card chart-card mb-4">
                        <div class="card-header bg-white border-bottom-0 p-0">
                            <ul id="wallets" class="nav nav-v2 nav-primary nav-justified d-block d-xl-flex w-100"
                                role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center py-3 px-4 active"
                                       href="#wordcloud-tab" role="tab" aria-selected="true"
                                       data-toggle="tab">
                                        <i class="fas fa-cloud mr-2"></i>
                                        <div>
                                            <strong>作者关键词词云</strong>
                                            <small class="d-block text-muted">热门作者分布</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center py-3 px-4"
                                       href="#trend-tab" role="tab" aria-selected="false"
                                       data-toggle="tab">
                                        <i class="fas fa-chart-line mr-2"></i>
                                        <div>
                                            <strong>热搜趋势</strong>
                                            <small class="d-block text-muted">时间趋势分析</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center py-3 px-4" href="#ranking-tab"
                                       role="tab" aria-selected="false"
                                       data-toggle="tab">
                                        <i class="fas fa-trophy mr-2"></i>
                                        <div>
                                            <strong>热度排行</strong>
                                            <small class="d-block text-muted">TOP排行榜</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link d-flex align-items-center py-3 px-4" href="#distribution-tab"
                                       role="tab" aria-selected="false"
                                       data-toggle="tab">
                                        <i class="fas fa-chart-pie mr-2"></i>
                                        <div>
                                            <strong>热度分布</strong>
                                            <small class="d-block text-muted">用户分布情况</small>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div id="walletsContent" class="card-body tab-content">
                            <div class="tab-pane fade show active" id="wordcloud-tab" role="tabpanel">
                                <div class="text-center mb-3">
                                    <h5 class="text-primary">📊 作者关键词词云图</h5>
                                    <p class="text-muted">展示热门作者的分布情况，字体大小代表热度</p>
                                </div>
                                <div style="height: 450px; background: #f8f9fa; border-radius: 10px; padding: 10px;">
                                    {{ wordcloud|safe }}
                                </div>
                            </div>

                            <div class="tab-pane fade" id="trend-tab" role="tabpanel">
                                <div class="text-center mb-3">
                                    <h5 class="text-success">📈 热搜趋势分析</h5>
                                    <p class="text-muted">显示热搜数据随时间的变化趋势</p>
                                </div>
                                <div style="height: 450px; background: #f8f9fa; border-radius: 10px; padding: 10px;">
                                    {{ bar2|safe }}
                                </div>
                            </div>

                            <div class="tab-pane fade" id="ranking-tab" role="tabpanel">
                                <div class="text-center mb-3">
                                    <h5 class="text-warning">🏆 热度排行榜</h5>
                                    <p class="text-muted">按照点赞数排序的热门内容TOP30</p>
                                </div>
                                <div style="height: 450px; background: #f8f9fa; border-radius: 10px; padding: 10px;">
                                    {{ funnel3|safe }}
                                </div>
                            </div>

                            <div class="tab-pane fade" id="distribution-tab" role="tabpanel">
                                <div class="text-center mb-3">
                                    <h5 class="text-info">🥧 用户热度分布</h5>
                                    <p class="text-muted">不同用户的热度分布饼图</p>
                                </div>
                                <div style="height: 450px; background: #f8f9fa; border-radius: 10px; padding: 10px;">
                                    {{ pie4|safe }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细分析区域 -->
            <div class="row">
                <div class="col-md-6 col-xl-4 mb-4">
                    <div class="card chart-card h-100">
                        <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <h5 class="mb-0">
                                <i class="fas fa-fire mr-2"></i>热搜内容精选
                            </h5>
                            <small>随机展示热门内容</small>
                        </div>
                        <div class="card-body">
                            <div class="content-card">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-quote-left text-primary mr-2"></i>
                                    <strong>热门内容</strong>
                                </div>
                                <div class="mb-0 text-muted">{{ content|safe }}</div>
                            </div>
                            <div class="mt-3 text-center">
                                <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                                    <i class="fas fa-sync-alt mr-1"></i>刷新内容
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-4 mb-4">
                    <div class="card chart-card h-100">
                        <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <h5 class="mb-0">
                                <i class="fas fa-tags mr-2"></i>帖子类别分布
                            </h5>
                            <small>不同类别内容占比</small>
                        </div>
                        <div class="card-body p-2">
                            <div style="height: 350px;">
                                {{ pie5|safe }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-4 mb-4">
                    <div class="card chart-card h-100">
                        <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar mr-2"></i>类别数据统计
                            </h5>
                            <small>各类别详细数据</small>
                        </div>
                        <div class="card-body p-2">
                            <div style="height: 350px;">
                                {{ bar6|safe }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能快捷入口 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card chart-card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-rocket mr-2"></i>功能导航
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-2 col-6 mb-3">
                                    <a href="/table1/" class="function-link">
                                        <div class="function-card bg-primary text-white">
                                            <i class="fas fa-table fa-2x mb-2"></i>
                                            <h6>热搜数据表</h6>
                                            <small>查看详细热搜数据</small>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <a href="/table2/" class="function-link">
                                        <div class="function-card bg-success text-white">
                                            <i class="fas fa-search fa-2x mb-2"></i>
                                            <h6>搜索数据表</h6>
                                            <small>查看搜索数据详情</small>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <a href="/predict/" class="function-link">
                                        <div class="function-card bg-warning text-white">
                                            <i class="fas fa-brain fa-2x mb-2"></i>
                                            <h6>情感分析</h6>
                                            <small>AI智能情感分析</small>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <a href="/bi/" class="function-link">
                                        <div class="function-card bg-info text-white">
                                            <i class="fas fa-chart-pie fa-2x mb-2"></i>
                                            <h6>数据大屏</h6>
                                            <small>可视化数据大屏</small>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <a href="http://localhost:8501/" target="_blank" class="function-link">
                                        <div class="function-card bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                            <i class="fas fa-rocket fa-2x mb-2"></i>
                                            <h6>情感分析系统</h6>
                                            <small>weibo-sent-analyse</small>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 col-6 mb-3">
                                    <a href="/sentiment-iframe/" class="function-link">
                                        <div class="function-card bg-gradient text-white" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                            <i class="fas fa-window-restore fa-2x mb-2"></i>
                                            <h6>嵌入式分析</h6>
                                            <small>iframe集成版本</small>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}
        *{margin:0;padding:0;border-width:0;border-style:solid;box-sizing:border-box;border-color:var(--x-default-border-color,#E5E7EB)}
        .py-4{padding-top:4px;padding-bottom:4px}
        .px-3{padding-left:3px;padding-right:3px}
        .px-md-4{padding-right:undefined}
        .text-center{text-align:center}
        .mb-3{margin-bottom:3px}
        .mb-0{margin-bottom:0}
        .mb-4{margin-bottom:4px}
        .mb-1{margin-bottom:1px}
        .bg-white{background-color:rgb(255,255,255)}
        .p-0{padding:0}
        .w-100{width:100px}
        .py-3{padding-top:3px;padding-bottom:3px}
        .px-4{padding-left:4px;padding-right:4px}
        .mr-2{margin-right:2px}
        .h-100{height:100px}
        .text-white{color:rgb(255,255,255)}
        .mb-2{margin-bottom:2px}
        .mt-3{margin-top:3px}
        .mr-1{margin-right:1px}
        .p-2{padding:2px}
        .py-4{padding-top:4px;padding-bottom:4px}
        .px-3{padding-left:3px;padding-right:3px}
        .px-md-4{padding-right:undefined}
        .text-center{text-align:center}
        .mb-3{margin-bottom:3px}
        .mb-0{margin-bottom:0}
        .mb-4{margin-bottom:4px}
        .mb-1{margin-bottom:1px}
        .bg-white{background-color:rgb(255,255,255)}
        .p-0{padding:0}
        .w-100{width:100px}
        .py-3{padding-top:3px;padding-bottom:3px}
        .px-4{padding-left:4px;padding-right:4px}
        .mr-2{margin-right:2px}
        .h-100{height:100px}
        .text-white{color:rgb(255,255,255)}
        .mb-2{margin-bottom:2px}
        .mt-3{margin-top:3px}
        .mr-1{margin-right:1px}
        .p-2{padding:2px}
        .py-4{padding-top:4px;padding-bottom:4px}
        .px-3{padding-left:3px;padding-right:3px}
        .px-md-4{padding-right:undefined}
        .text-center{text-align:center}
        .mb-3{margin-bottom:3px}
        .mb-0{margin-bottom:0}
        .mb-4{margin-bottom:4px}
        .mb-1{margin-bottom:1px}
        .bg-white{background-color:rgb(255,255,255)}
        .p-0{padding:0}
        .w-100{width:100px}
        .py-3{padding-top:3px;padding-bottom:3px}
        .px-4{padding-left:4px;padding-right:4px}
        .mr-2{margin-right:2px}
        .h-100{height:100px}
        .text-white{color:rgb(255,255,255)}
        .mb-2{margin-bottom:2px}
        .mt-3{margin-top:3px}
        .mr-1{margin-right:1px}
        .p-2{padding:2px}
        .py-4{padding-top:4px;padding-bottom:4px}
        .px-3{padding-left:3px;padding-right:3px}
        .px-md-4{padding-right:undefined}
        .text-center{text-align:center}
        .mb-3{margin-bottom:3px}
        .mb-0{margin-bottom:0}
        .mb-4{margin-bottom:4px}
        .mb-1{margin-bottom:1px}
        .bg-white{background-color:rgb(255,255,255)}
        .p-0{padding:0}
        .w-100{width:100px}
        .py-3{padding-top:3px;padding-bottom:3px}
        .px-4{padding-left:4px;padding-right:4px}
        .mr-2{margin-right:2px}
        .h-100{height:100px}
        .text-white{color:rgb(255,255,255)}
        .mb-2{margin-bottom:2px}
        .mt-3{margin-top:3px}
        .mr-1{margin-right:1px}
        .p-2{padding:2px}
