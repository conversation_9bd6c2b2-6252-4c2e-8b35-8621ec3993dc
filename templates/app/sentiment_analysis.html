<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TextCNN情感分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            padding: 30px;
            max-width: 1200px;
        }
        .header-section {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }
        .analysis-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }
        .result-section {
            background: #ffffff;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        .emotion-badge {
            font-size: 1.2em;
            padding: 8px 16px;
            border-radius: 25px;
        }
        .confidence-bar {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.5s ease;
        }
        .model-selector {
            background: #fff;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 页面标题 -->
            <div class="header-section">
                <h1><i class="fas fa-brain"></i> TextCNN情感分析系统</h1>
                <p class="mb-0">基于深度学习的中文文本情感识别与分析平台</p>
            </div>

            <!-- 分析表单 -->
            <div class="analysis-form">
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="model-selector">
                                <h5><i class="fas fa-cog"></i> 分析模式</h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="analysis_type" value="single" 
                                           {% if not analysis_type or analysis_type == 'single' %}checked{% endif %}>
                                    <label class="form-check-label">
                                        <i class="fas fa-file-text"></i> 单文本分析
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="analysis_type" value="batch"
                                           {% if analysis_type == 'batch' %}checked{% endif %}>
                                    <label class="form-check-label">
                                        <i class="fas fa-database"></i> 批量数据分析
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="model-selector">
                                <h5><i class="fas fa-robot"></i> 分析模型</h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="model_type" value="textcnn"
                                           {% if not model_type or model_type == 'textcnn' %}checked{% endif %}>
                                    <label class="form-check-label">
                                        <i class="fas fa-brain"></i> TextCNN深度学习模型
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="model_type" value="snownlp"
                                           {% if model_type == 'snownlp' %}checked{% endif %}>
                                    <label class="form-check-label">
                                        <i class="fas fa-chart-line"></i> SnowNLP统计模型
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3" id="text-input-section">
                        <label for="text" class="form-label"><i class="fas fa-edit"></i> 输入文本</label>
                        <textarea class="form-control" id="text" name="text" rows="4" 
                                  placeholder="请输入要分析的中文文本...">{{ text }}</textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search"></i> 开始分析
                        </button>
                    </div>
                </form>
            </div>

            <!-- 分析结果 -->
            {% if result or emotion_counts %}
            <div class="result-section">
                {% if analysis_type == 'single' and result %}
                <!-- 单文本分析结果 -->
                <h3><i class="fas fa-chart-pie"></i> 分析结果</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="stats-card">
                            <h4>情感类别</h4>
                            <span class="emotion-badge badge bg-primary">{{ result.emotion_chinese }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card">
                            <h4>置信度</h4>
                            <h2>{{ result.confidence|floatformat:2 }}%</h2>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="chart-container">
                            {{ pie_chart|safe }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            {{ confidence_chart|safe }}
                        </div>
                    </div>
                </div>

                {% elif analysis_type == 'batch' and emotion_counts %}
                <!-- 批量分析结果 -->
                <h3><i class="fas fa-chart-bar"></i> 批量分析结果</h3>
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4>总分析数</h4>
                            <h2>{{ total_count }}</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4>积极情感</h4>
                            <h2>{{ emotion_counts.happiness|add:emotion_counts.like }}</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4>消极情感</h4>
                            <h2>{{ emotion_counts.sadness|add:emotion_counts.anger|add:emotion_counts.disgust|add:emotion_counts.fear }}</h2>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h4>中性情感</h4>
                            <h2>{{ emotion_counts.neutral|add:emotion_counts.surprise }}</h2>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="chart-container">
                            {{ pie_chart|safe }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            {{ bar_chart|safe }}
                        </div>
                    </div>
                </div>

                <!-- 详细结果表格 -->
                {% if results %}
                <div class="mt-4">
                    <h4><i class="fas fa-table"></i> 详细分析结果（前10条）</h4>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>文本内容</th>
                                    <th>情感类别</th>
                                    <th>置信度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in results %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ result.text|truncatechars:50 }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ result.emotion_chinese }}</span>
                                    </td>
                                    <td>{{ result.confidence|floatformat:2 }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
                {% endif %}
            </div>
            {% endif %}

            <!-- 功能说明 -->
            <div class="result-section">
                <h4><i class="fas fa-info-circle"></i> 功能说明</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h5>TextCNN模型</h5>
                        <ul>
                            <li>基于深度学习的卷积神经网络</li>
                            <li>支持7种情感分类：喜欢、厌恶、开心、难过、愤怒、惊讶、害怕</li>
                            <li>针对中文微博文本优化训练</li>
                            <li>分析准确率更高，适合复杂情感识别</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>SnowNLP模型</h5>
                        <ul>
                            <li>基于统计学习的自然语言处理库</li>
                            <li>支持积极、消极、中性三分类</li>
                            <li>处理速度快，资源占用少</li>
                            <li>适合快速情感倾向判断</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 根据分析类型显示/隐藏文本输入框
        document.querySelectorAll('input[name="analysis_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const textSection = document.getElementById('text-input-section');
                if (this.value === 'batch') {
                    textSection.style.display = 'none';
                } else {
                    textSection.style.display = 'block';
                }
            });
        });

        // 页面加载时检查当前选择
        document.addEventListener('DOMContentLoaded', function() {
            const batchRadio = document.querySelector('input[name="analysis_type"][value="batch"]');
            const textSection = document.getElementById('text-input-section');
            if (batchRadio && batchRadio.checked) {
                textSection.style.display = 'none';
            }
        });
    </script>
</body>
</html>
