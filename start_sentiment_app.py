#!/usr/bin/env python3
"""
启动weibo-sent-analyse-master Streamlit应用的脚本
"""
import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class SentimentAppManager:
    def __init__(self):
        self.process = None
        self.app_dir = Path(__file__).parent / "weibo-sent-analyse-master"
        self.port = 8501  # Streamlit默认端口
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        try:
            import streamlit
            print("✅ Streamlit已安装")
        except ImportError:
            print("❌ Streamlit未安装，正在安装...")
            subprocess.run([sys.executable, "-m", "pip", "install", "streamlit"], check=True)
            print("✅ Streamlit安装完成")
            
        # 检查其他依赖
        dependencies = ['pandas', 'numpy', 'torch', 'PIL', 'pyecharts']
        for dep in dependencies:
            try:
                __import__(dep)
                print(f"✅ {dep}已安装")
            except ImportError:
                print(f"⚠️ {dep}未安装，可能需要手动安装")
    
    def start_app(self):
        """启动Streamlit应用"""
        if not self.app_dir.exists():
            print(f"❌ 应用目录不存在: {self.app_dir}")
            return False
            
        webtest_file = self.app_dir / "webtest.py"
        if not webtest_file.exists():
            print(f"❌ webtest.py文件不存在: {webtest_file}")
            return False
            
        print(f"🚀 启动Streamlit应用...")
        print(f"📁 工作目录: {self.app_dir}")
        print(f"🌐 端口: {self.port}")
        
        try:
            # 切换到应用目录
            os.chdir(self.app_dir)
            
            # 启动Streamlit应用
            cmd = [
                sys.executable, "-m", "streamlit", "run", "webtest.py",
                "--server.port", str(self.port),
                "--server.address", "localhost",
                "--server.headless", "true",
                "--browser.gatherUsageStats", "false"
            ]
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待应用启动
            time.sleep(3)
            
            if self.process.poll() is None:
                print(f"✅ Streamlit应用已启动")
                print(f"🔗 访问地址: http://localhost:{self.port}")
                return True
            else:
                stdout, stderr = self.process.communicate()
                print(f"❌ 应用启动失败")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动应用时出错: {str(e)}")
            return False
    
    def stop_app(self):
        """停止Streamlit应用"""
        if self.process and self.process.poll() is None:
            print("🛑 正在停止Streamlit应用...")
            self.process.terminate()
            try:
                self.process.wait(timeout=5)
                print("✅ 应用已停止")
            except subprocess.TimeoutExpired:
                print("⚠️ 强制终止应用...")
                self.process.kill()
                self.process.wait()
                print("✅ 应用已强制停止")
            self.process = None
    
    def is_running(self):
        """检查应用是否正在运行"""
        return self.process and self.process.poll() is None
    
    def get_status(self):
        """获取应用状态"""
        if self.is_running():
            return {
                'status': 'running',
                'url': f'http://localhost:{self.port}',
                'pid': self.process.pid
            }
        else:
            return {
                'status': 'stopped',
                'url': None,
                'pid': None
            }

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 接收到停止信号，正在关闭应用...")
    if hasattr(signal_handler, 'app_manager'):
        signal_handler.app_manager.stop_app()
    sys.exit(0)

def main():
    """主函数"""
    app_manager = SentimentAppManager()
    signal_handler.app_manager = app_manager
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🔍 检查依赖...")
    app_manager.check_dependencies()
    
    print("🚀 启动情感分析应用...")
    if app_manager.start_app():
        print("✅ 应用启动成功！")
        print(f"🔗 访问地址: http://localhost:{app_manager.port}")
        print("按 Ctrl+C 停止应用")
        
        try:
            # 保持应用运行
            while app_manager.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            app_manager.stop_app()
    else:
        print("❌ 应用启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
