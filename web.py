'''
Author: 黑白灰01 <EMAIL>
Date: 2025-05-24 13:45:42
LastEditors: 黑白灰01 <EMAIL>
LastEditTime: 2025-05-28 17:53:38
FilePath: /system/web.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
from snownlp import SnowNLP
import gradio as gr


def analyze_sentiment(text):
    """
    使用 SnowNLP 分析中文文本的情感
    :param text: 输入的中文文本
    :return: 情感分数和类别
    """
    if not text:
        return 0.5, "中性"  # 如果输入为空，返回中性

    # 使用 SnowNLP 分析情感
    s = SnowNLP(text)
    sentiment_score = s.sentiments  # 情感分数（0到1之间）

    # 根据情感分数分类
    if sentiment_score > 0.6:
        sentiment_category = "积极"
    elif sentiment_score < 0.4:
        sentiment_category = "消极"
    else:
        sentiment_category = "中性"

    return sentiment_score, sentiment_category


# 定义 Gradio 界面
def sentiment_analysis_interface(text):
    score, category = analyze_sentiment(text)
    return f"情感分数: {score:.4f}", f"情感类别: {category}"


# 创建 Gradio 界面
interface = gr.Interface(
    fn=sentiment_analysis_interface,  # 处理函数
    inputs=gr.Textbox(lines=2, placeholder="请输入中文文本...", label="输入文本"),  # 输入组件
    outputs=[gr.Textbox(label="情感分数"), gr.Textbox(label="情感类别")],  # 输出组件
    title="情感分析",
    description="输入中文文本，分析情感分数和类别（积极、中性、消极）。",
    allow_flagging="never",  # 禁用默认的“标记”按钮
    live=False,  # 禁用实时更新
    submit_btn="分析",  # 将提交按钮改为“分析”
    clear_btn="清空",  # 将清除按钮改为“清空”
    examples=[
        ["今天天气真好，心情不错，一起去玩吧！"],
        ["这部电影太糟糕了，浪费时间。"],
        ["今天是一个普通的日子。"]
    ]
)

# 启动 Gradio 应用
interface.launch(server_name="127.0.0.1", server_port=7860, share=False)